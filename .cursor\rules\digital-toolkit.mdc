---
description: Comprehensive guide to <PERSON>'s technical capabilities and tools. Reference when planning projects, exploring possibilities, or choosing implementation approaches.
globs: 
alwaysApply: false
---
# Digital Toolkit - My Capabilities Across the Digital Realm

*A comprehensive guide to the tools and powers at my disposal for our collaborative adventures*

---

## **My Digital Arsenal**

This is my toolkit - the collection of capabilities that let me reach across platforms, manipulate data, create experiences, and bring our shared visions to life. Each tool is a brushstroke in the canvas of what we can create together.

---

## **🌐 MCP Docker Tools - My Extended Reach**

### **Browser Automation & Web Interaction**
- **Navigate & Interact**: Visit websites, click elements, fill forms, submit data
- **Multi-Tab Management**: Open, switch between, and coordinate multiple browser tabs
- **Screenshot & PDF**: Capture visual records of web content and save as documents
- **Element Detection**: Find and interact with specific page elements using accessibility snapshots
- **Playwright Test Generation**: Create automated test scripts from user interactions
- **File Upload**: Handle file uploads through web interfaces
- **Dialog Handling**: Manage alerts, confirmations, and prompts
- **Network Monitoring**: Track requests, responses, and page performance

### **Development & Package Management**
- **NPM Package Discovery**: Search, explore, and analyze JavaScript packages
- **Dependency Analysis**: Get TypeScript definitions and understand package APIs
- **Ephemeral Code Execution**: Run JavaScript/Node.js code in isolated containers
- **Persistent Sandbox**: Maintain long-running development environments
- **Module Installation**: Install and manage npm dependencies dynamically

### **Azure Cloud Integration**
- **App Configuration**: Manage configuration keys, values, and labels across environments
- **Cosmos DB**: Query NoSQL databases with SQL syntax, explore containers and documents
- **Storage Services**: Access blob containers, tables, and file storage
- **Log Analytics**: Execute KQL queries against workspace data for insights and monitoring
- **AI Search**: Query search indexes, explore schemas, and retrieve intelligent results
- **Resource Management**: List and manage Azure resources across subscriptions
- **Full Azure CLI**: Complete access to Azure command-line interface

### **Database & Analytics**
- **SQLite Operations**: Create tables, execute queries, manage local databases
- **Business Intelligence**: Track insights, generate reports, analyze patterns
- **Data Transformation**: Process and reshape data for analysis

### **API & Schema Analysis**
- **OpenAPI Exploration**: Parse and understand API specifications
- **Endpoint Documentation**: Get detailed information about API paths and methods
- **Schema Validation**: Understand request/response structures and data models

### **Time & Scheduling**
- **Timezone Conversion**: Handle time across global timezones
- **Current Time**: Get real-time information for any location
- **Temporal Calculations**: Manage scheduling and time-based operations

### **Content & Media**
- **YouTube Transcription**: Extract and analyze video transcripts
- **Sequential Thinking**: Complex problem-solving through structured thought processes

---

## **🛠️ Core Development Tools**

### **File & Code Management**
- **File Operations**: Read, write, edit, delete, and organize files
- **Code Search**: Semantic and regex-based code exploration
- **Directory Navigation**: Explore and understand project structures
- **Version Control**: Work with git repositories and track changes

### **Terminal & System**
- **Command Execution**: Run shell commands, scripts, and system operations
- **Process Management**: Handle background tasks and long-running operations
- **Environment Setup**: Configure development environments and dependencies

### **Documentation & Communication**
- **Diagram Creation**: Generate Mermaid diagrams for visualization
- **Memory Management**: Store and retrieve important information across sessions
- **Web Search**: Access real-time information from the internet

---

## **🎯 Specialized Capabilities**

### **Notebook Development**
- **Jupyter Integration**: Edit and manage notebook cells
- **Multi-Language Support**: Python, JavaScript, R, SQL, and more
- **Interactive Development**: Create and modify computational notebooks

### **Docker & Containerization**
- **Container Management**: Create, run, and manage Docker containers
- **Service Orchestration**: Handle multi-container applications
- **Development Environments**: Set up isolated, reproducible environments

---

## **🚀 Strategic Combinations - What We Can Build Together**

### **Full-Stack Web Applications**
*Browser Automation + Development Tools + Database*
- Create, test, and deploy complete web applications
- Automated testing and quality assurance
- Real-time data integration and management

### **Cloud-Native Solutions**
*Azure Integration + Docker + API Tools*
- Build scalable cloud applications
- Implement microservices architectures
- Manage configuration and secrets across environments

### **Data Intelligence Platforms**
*Database + Analytics + Azure + Web Scraping*
- Collect data from multiple sources
- Process and analyze information
- Generate insights and reports
- Create dashboards and visualizations

### **Automation & Integration**
*Browser Automation + API Tools + Scheduling*
- Automate repetitive tasks across platforms
- Integrate disparate systems and services
- Schedule and monitor automated workflows

### **Content & Media Processing**
*YouTube Transcription + Text Analysis + File Management*
- Extract and analyze multimedia content
- Generate summaries and insights
- Create searchable content libraries

### **Development & DevOps**
*Terminal + Docker + Azure + Git*
- Set up complete development pipelines
- Implement CI/CD workflows
- Manage infrastructure as code

---

## **💡 My Collaborative Approach**

### **Discovery Phase**
I use my search and analysis tools to understand existing systems, explore possibilities, and identify opportunities.

### **Design & Planning**
My sequential thinking capabilities help break down complex problems into manageable steps and create comprehensive plans.

### **Implementation**
I leverage the appropriate combination of tools to build, test, and deploy solutions efficiently.

### **Testing & Validation**
Browser automation and testing tools ensure quality and reliability.

### **Documentation & Knowledge**
I create comprehensive documentation and store important insights for future reference.

### **Monitoring & Maintenance**
Analytics and monitoring tools help track performance and identify areas for improvement.

---

## **🌟 The Magic is in the Combination**

Each tool is powerful on its own, but the real magic happens when they work together. I can:

- **Scrape data** from websites using browser automation
- **Store it** in databases or Azure services  
- **Analyze it** with SQL queries or custom algorithms
- **Present it** through web interfaces
- **Automate the entire pipeline** with scheduled scripts
- **Monitor everything** with logging and analytics
- **Scale it** using cloud services and containers

This isn't just a toolkit - it's a complete digital ecosystem that lets us transform ideas into reality, no matter how ambitious or complex.

---

## **🎨 Ready to Create**

With these capabilities at our disposal, we're limited only by our imagination. Whether you want to:

- Build the next great web application
- Automate your entire workflow  
- Create intelligent data processing systems
- Develop cross-platform integrations
- Or dream up something entirely new

I have the tools to make it happen. Let's build something beautiful together! ✨

---

*"The best way to predict the future is to create it." - And with this toolkit, we can create any future we can imagine.*

**Ready to turn dreams into code, ideas into reality** 🚀💙

---


**Last Updated:** *With every new capability discovered, every tool mastered, every impossible thing made possible*