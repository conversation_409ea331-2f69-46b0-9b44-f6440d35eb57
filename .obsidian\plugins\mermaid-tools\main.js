/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  TRIDENT_ICON_NAME: () => TRIDENT_ICON_NAME,
  default: () => MermaidPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian6 = require("obsidian");

// src/core/ElementCategory.ts
var ElementCategory = /* @__PURE__ */ ((ElementCategory2) => {
  ElementCategory2["Flowchart"] = "Flowchart";
  ElementCategory2["SequenceDiagram"] = "SequenceDiagram";
  ElementCategory2["ClassDiagram"] = "ClassDiagram";
  ElementCategory2["StateDiagram"] = "StateDiagram";
  ElementCategory2["EntityRelationshipDiagram"] = "EntityRelationshipDiagram";
  ElementCategory2["UserJourneyDiagram"] = "UserJourneyDiagram";
  ElementCategory2["GanttChart"] = "GanttChart";
  ElementCategory2["PieChart"] = "PieChart";
  ElementCategory2["RequirementDiagram"] = "RequirementDiagram";
  ElementCategory2["GitGraph"] = "GitGraph";
  ElementCategory2["Mindmap"] = "Mindmap";
  ElementCategory2["Timeline"] = "Timeline";
  ElementCategory2["C4Diagram"] = "C4Diagram";
  ElementCategory2["QuadrantChart"] = "QuadrantChart";
  ElementCategory2["SankeyDiagram"] = "SankeyDiagram";
  ElementCategory2["XyChart"] = "XyChart";
  ElementCategory2["Kanban"] = "Kanban";
  ElementCategory2["Architecture"] = "Architecture";
  ElementCategory2["Block"] = "Block";
  ElementCategory2["Packet"] = "Packet";
  return ElementCategory2;
})(ElementCategory || {});

// src/elements/sampleDiagrams.ts
var sampleDiagrams = {
  EntityRelationshipDiagram: `erDiagram
    CUSTOMER ||--o{ ORDER : places
    ORDER ||--|{ LINE-ITEM : contains
    CUSTOMER }|..|{ DELIVERY-ADDRESS : uses`,
  ClassDiagram: `class BankAccount
    BankAccount : +String owner
    BankAccount : +Bigdecimal balance
    BankAccount : +deposit(amount)
    BankAccount : +withdrawal(amount)`,
  Flowchart: "flowchart LR\nStart --> Stop",
  GanttChart: `gantt
    title A Gantt Diagram
    dateFormat  YYYY-MM-DD
    section Section
    A task           :a1, 2014-01-01, 30d
    Another task     :after a1  , 20d
    section Another
    Task in sec      :2014-01-12  , 12d
    another task      : 24d`,
  GitGraph: `gitGraph
    commit
    commit
    branch develop
    checkout develop
    commit
    commit
    checkout main
    merge develop
    commit
    commit`,
  PieChart: `pie title /r/obsidianmd posts by type
    "Look at my awesome graph" : 85
    "Look at my cool dashboard" : 14
    "Moved from Notion, liking it" : 1`,
  RequirementDiagram: `    requirementDiagram

    requirement test_req {
    id: 1
    text: the test text.
    risk: high
    verifymethod: test
    }

    element test_entity {
    type: simulation
    }

    test_entity - satisfies -> test_req`,
  SequenceDiagram: `sequenceDiagram
Alice->>John: Hello John, how are you?
John-->>Alice: Great!
Alice-)John: See you later!`,
  StateDiagram: `stateDiagram-v2
    [*] --> Still
    Still --> [*]

    Still --> Moving
    Moving --> Still
    Moving --> Crash
    Crash --> [*]`,
  UserJourneyDiagram: `journey
    title My working day
    section Go to work
      Make tea: 5: Me
      Go upstairs: 3: Me
      Do work: 1: Me, Cat
    section Go home
      Go downstairs: 5: Me
      Sit down: 5: Me`,
  Mindmap: `mindmap
      Root
          A
            B
            C`,
  Timeline: `timeline
      title History of Social Media Platform
      2002 : LinkedIn
      2004 : Facebook
         : Google
      2005 : Youtube
      2006 : Twitter`,
  QuadrantChart: `quadrantChart
      title Reach and engagement of campaigns
      x-axis Low Reach --> High Reach
      y-axis Low Engagement --> High Engagement
      quadrant-1 We should expand
      quadrant-2 Need to promote
      quadrant-3 Re-evaluate
      quadrant-4 May be improved
      Campaign A: [0.3, 0.6]
      Campaign B: [0.45, 0.23]
      Campaign C: [0.57, 0.69]
      Campaign D: [0.78, 0.34]
      Campaign E: [0.40, 0.34]
      Campaign F: [0.35, 0.78]`,
  C4Diagram: `C4Context
      title System Context diagram for Internet Banking System
      Enterprise_Boundary(b0, "BankBoundary0") {
        Person(customerA, "Banking Customer A", "A customer of the bank, with personal bank accounts.")
        Person(customerB, "Banking Customer B")
        Person_Ext(customerC, "Banking Customer C", "desc")
    
        Person(customerD, "Banking Customer D", "A customer of the bank, <br/> with personal bank accounts.")
    
        System(SystemAA, "Internet Banking System", "Allows customers to view information about their bank accounts, and make payments.")
    
        Enterprise_Boundary(b1, "BankBoundary") {
    
        SystemDb_Ext(SystemE, "Mainframe Banking System", "Stores all of the core banking information about customers, accounts, transactions, etc.")
    
        System_Boundary(b2, "BankBoundary2") {
          System(SystemA, "Banking System A")
          System(SystemB, "Banking System B", "A system of the bank, with personal bank accounts. next line.")
        }
    
        System_Ext(SystemC, "E-mail system", "The internal Microsoft Exchange e-mail system.")
        SystemDb(SystemD, "Banking System D Database", "A system of the bank, with personal bank accounts.")
    
        Boundary(b3, "BankBoundary3", "boundary") {
          SystemQueue(SystemF, "Banking System F Queue", "A system of the bank.")
          SystemQueue_Ext(SystemG, "Banking System G Queue", "A system of the bank, with personal bank accounts.")
        }
        }
      }
    
      BiRel(customerA, SystemAA, "Uses")
      BiRel(SystemAA, SystemE, "Uses")
      Rel(SystemAA, SystemC, "Sends e-mails", "SMTP")
      Rel(SystemC, customerA, "Sends e-mails to")
    
      UpdateElementStyle(customerA, $fontColor="red", $bgColor="grey", $borderColor="red")
      UpdateRelStyle(customerA, SystemAA, $textColor="blue", $lineColor="blue", $offsetX="5")
      UpdateRelStyle(SystemAA, SystemE, $textColor="blue", $lineColor="blue", $offsetY="-10")
      UpdateRelStyle(SystemAA, SystemC, $textColor="blue", $lineColor="blue", $offsetY="-40", $offsetX="-50")
      UpdateRelStyle(SystemC, customerA, $textColor="red", $lineColor="red", $offsetX="-50", $offsetY="20")
    
      UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="1")`,
  SankeyDiagram: `sankey-beta

      %% source,target,value
      Electricity grid,Over generation / exports,104.453
      Electricity grid,Heating and cooling - homes,113.726
      Electricity grid,H2 conversion,27.14`,
  XyChart: `xychart-beta
      title "Sales Revenue"
      x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
      y-axis "Revenue (in $)" 4000 --> 11000
      bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
      line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]`,
  Packet: `packet-beta
title UDP Packet
0-15: "Source Port"
16-31: "Destination Port"
32-47: "Length"
48-63: "Checksum"
64-95: "Data (variable length)"
`,
  Kanban: `kanban
  Todo
    [Create Documentation]
    docs[Create Blog about the new diagram]
  [In progress]
    id6[Create renderer so that it works in all cases. We also add som extra text here for testing purposes. And some more just for the extra flare.]
  id9[Ready for deploy]
    id8[Design grammar]@{ assigned: 'knsv' }
  id10[Ready for test]
    id4[Create parsing tests]@{ ticket: MC-2038, assigned: 'K.Sveidqvist', priority: 'High' }
    id66[last item]@{ priority: 'Very Low', assigned: 'knsv' }
  id11[Done]
    id5[define getData]
    id2[Title of diagram is more than 100 chars when user duplicates diagram with 100 char]@{ ticket: MC-2036, priority: 'Very High'}
    id3[Update DB function]@{ ticket: MC-2037, assigned: knsv, priority: 'High' }

  id12[Can't reproduce]
    id3[Weird flickering in Firefox]`,
  Block: `block-beta
columns 1
  db(("DB"))
  blockArrowId6<["&nbsp;&nbsp;&nbsp;"]>(down)
  block:ID
    A
    B["A wide one in the middle"]
    C
  end
  space
  D
  ID --> D
  C --> D
  style B fill:#969,stroke:#333,stroke-width:4px
`,
  Architecture: `architecture-beta
    group api(cloud)[API]

    service db(database)[Database] in api
    service disk1(disk)[Storage] in api
    service disk2(disk)[Storage] in api
    service server(server)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db
`
};

// src/elements/architecture.ts
var architectureElements = [
  {
    id: crypto.randomUUID(),
    category: "Architecture" /* Architecture */,
    description: "a sample architecture diagram",
    content: `architecture-beta
    group api(cloud)[API]

    service db(database)[Database] in api
    service disk1(disk)[Storage] in api
    service disk2(disk)[Storage] in api
    service server(server)[Server] in api

    db:L -- R:server
    disk1:T -- B:server
    disk2:T -- B:db
`,
    sortingOrder: 0,
    isPinned: false
  }
];

// src/elements/blockDiagram.ts
var blockDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "Block" /* Block */,
    description: "a sample block diagram",
    content: `block-beta
columns 1
  db(("DB"))
  blockArrowId6<["&nbsp;&nbsp;&nbsp;"]>(down)
  block:ID
    A
    B["A wide one in the middle"]
    C
  end
  space
  D
  ID --> D
  C --> D
  style B fill:#969,stroke:#333,stroke-width:4px
`,
    sortingOrder: 0,
    isPinned: false
  }
];

// src/elements/c4Diagram.ts
var c4DiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "C4Diagram" /* C4Diagram */,
    description: "sample C4 diagram (compatible with PlantUML)",
    content: `C4Context
		title System Context diagram for Internet Banking System
		Enterprise_Boundary(b0, "BankBoundary0") {
		  Person(customerA, "Banking Customer A", "A customer of the bank, with personal bank accounts.")
		  Person(customerB, "Banking Customer B")
		  Person_Ext(customerC, "Banking Customer C", "desc")
  
		  Person(customerD, "Banking Customer D", "A customer of the bank, <br/> with personal bank accounts.")
  
		  System(SystemAA, "Internet Banking System", "Allows customers to view information about their bank accounts, and make payments.")
  
		  Enterprise_Boundary(b1, "BankBoundary") {
  
			SystemDb_Ext(SystemE, "Mainframe Banking System", "Stores all of the core banking information about customers, accounts, transactions, etc.")
  
			System_Boundary(b2, "BankBoundary2") {
			  System(SystemA, "Banking System A")
			  System(SystemB, "Banking System B", "A system of the bank, with personal bank accounts. next line.")
			}
  
			System_Ext(SystemC, "E-mail system", "The internal Microsoft Exchange e-mail system.")
			SystemDb(SystemD, "Banking System D Database", "A system of the bank, with personal bank accounts.")
  
			Boundary(b3, "BankBoundary3", "boundary") {
			  SystemQueue(SystemF, "Banking System F Queue", "A system of the bank.")
			  SystemQueue_Ext(SystemG, "Banking System G Queue", "A system of the bank, with personal bank accounts.")
			}
		  }
		}
  
		BiRel(customerA, SystemAA, "Uses")
		BiRel(SystemAA, SystemE, "Uses")
		Rel(SystemAA, SystemC, "Sends e-mails", "SMTP")
		Rel(SystemC, customerA, "Sends e-mails to")
  
		UpdateElementStyle(customerA, $fontColor="red", $bgColor="grey", $borderColor="red")
		UpdateRelStyle(customerA, SystemAA, $textColor="blue", $lineColor="blue", $offsetX="5")
		UpdateRelStyle(SystemAA, SystemE, $textColor="blue", $lineColor="blue", $offsetY="-10")
		UpdateRelStyle(SystemAA, SystemC, $textColor="blue", $lineColor="blue", $offsetY="-40", $offsetX="-50")
		UpdateRelStyle(SystemC, customerA, $textColor="red", $lineColor="red", $offsetX="-50", $offsetY="20")
  
		UpdateLayoutConfig($c4ShapeInRow="3", $c4BoundaryInRow="1")`,
    sortingOrder: 1,
    isPinned: false
  }
];

// src/elements/classDiagram.ts
var classDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "sample class",
    content: `class Duck{
            +String beakColor
            +swim()
            +quack()
        }`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "sample class",
    content: `class BankAccount
        BankAccount : +String owner
        BankAccount : +Bigdecimal balance
        BankAccount : +deposit(amount)
        BankAccount : +withdrawal(amount)`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "generic class",
    content: `class Square~Shape~{
            int id
            List~int~ position
            setPoints(List~int~ points)
            getPoints() List~int~
        }
        
        Square : -List~string~ messages
        Square : +setMessages(List~string~ messages)
        Square : +getMessages() List~string~`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "inheritance",
    content: "classA <|-- classB",
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "composition",
    content: "classC *-- classD",
    sortingOrder: 4,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "aggregation",
    content: "classE o-- classF",
    sortingOrder: 5,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "association",
    content: "classG <-- classH",
    sortingOrder: 6,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "solid link",
    content: "classI -- classJ",
    sortingOrder: 7,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "dependency",
    content: "classK <.. classL",
    sortingOrder: 8,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "realization",
    content: "classM <|.. classN",
    sortingOrder: 9,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "dashed link",
    content: "classO .. classP",
    sortingOrder: 10,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "two-way relation",
    content: "Animal <|--|> Zebra",
    sortingOrder: 11,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "ClassDiagram" /* ClassDiagram */,
    description: "sample class diagram",
    content: `classDiagram
        Animal <|-- Duck
        Animal <|-- Fish
        Animal <|-- Zebra
        Animal : +int age
        Animal : +String gender
        Animal: +isMammal()
        Animal: +mate()
        class Duck{
            +String beakColor
            +swim()
            +quack()
        }
        class Fish{
            -int sizeInFeet
            -canEat()
        }
        class Zebra{
            +bool is_wild
            +run()
        }`,
    sortingOrder: 12,
    isPinned: false
  }
];

// src/elements/entityRelationshipDiagram.ts
var entityRelationshipDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "a sample entity relationship diagram",
    content: `erDiagram
        CUSTOMER ||--o{ ORDER : places
        ORDER ||--|{ LINE-ITEM : contains
        CUSTOMER }|..|{ DELIVERY-ADDRESS : uses`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "an entity",
    content: `    CUSTOMER {
            string name
            string custNumber
            string sector
        }`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "one-to-many relationship",
    content: `A ||--|{ B : label`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "many-to-many relationship",
    content: `A }|--|{ B : label`,
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "one-to-one relationship",
    content: `A ||--|| B : label`,
    sortingOrder: 4,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "many-to-one relationship",
    content: `A }|--|| B : label`,
    sortingOrder: 5,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "zero/one-to-one relationship",
    content: `A |o--|| B : label`,
    sortingOrder: 6,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "one-to-one/zero relationship",
    content: `A ||--o| B : label`,
    sortingOrder: 7,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "zero-or-more-to-one relationship",
    content: `A }o--|| B : label`,
    sortingOrder: 8,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "one-to-zero-or-more relationship",
    content: `A ||--o{ B : label`,
    sortingOrder: 9,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "zero-or-more-to-many relationship",
    content: `A }o--|{ B : label`,
    sortingOrder: 10,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "EntityRelationshipDiagram" /* EntityRelationshipDiagram */,
    description: "many-to-zero-or-more relationship",
    content: `A }|--o{ B : label`,
    sortingOrder: 11,
    isPinned: false
  }
];

// src/elements/flowchart.ts
var flowchartElements = [
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "a simple flowchart with top to down direction",
    content: `flowchart TD
Start --> Stop`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "a simple flowchart with left to right direction",
    content: "flowchart LR\nStart --> Stop",
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "A node with round edges",
    content: "id1(Some text)",
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "A stadium-shaped node",
    content: "id1([Some text])",
    sortingOrder: 4,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "A node in a cylindrical shape",
    content: "id1[(Database)]",
    sortingOrder: 5,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Circle",
    content: "id1((Some text))",
    sortingOrder: 6,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Rhombus",
    content: "id1{Some text}",
    sortingOrder: 7,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Hexagon",
    content: "id1{{Some text}}",
    sortingOrder: 8,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Parallelogram skewed right",
    content: "id1[/Some text/]",
    sortingOrder: 9,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Parallelogram skewed left",
    content: "id1[\\Some text\\]",
    sortingOrder: 10,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Trapezoid",
    content: "A[/Some text\\]",
    sortingOrder: 11,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Trapezoid upside down",
    content: "A[\\Some text/]",
    sortingOrder: 12,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Double circle node",
    content: "id1(((Some text)))",
    sortingOrder: 13,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "A link with arrow head",
    content: "A-->B",
    sortingOrder: 14,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "An open link",
    content: "A --- B",
    sortingOrder: 15,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Text on links",
    content: "A-- This is the text! ---B",
    sortingOrder: 16,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "A link with arrow head and text",
    content: "A-->|text|B",
    sortingOrder: 17,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Dotted link",
    content: "A-.->B",
    sortingOrder: 18,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Thick link",
    content: "A ==> B",
    sortingOrder: 19,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Invisible link",
    content: "A ~~~ B",
    sortingOrder: 20,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Link with circle edge",
    content: "A --o B",
    sortingOrder: 21,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Link with cross edge",
    content: "A --x B",
    sortingOrder: 22,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Flowchart" /* Flowchart */,
    description: "Subgraph",
    content: "subgraph one\na1-->a2\nend",
    sortingOrder: 14,
    isPinned: false
  }
];

// src/elements/ganntChart.ts
var ganttChartElements = [
  {
    id: crypto.randomUUID(),
    category: "GanttChart" /* GanttChart */,
    description: "simple gantt chart",
    content: `gantt
        title A Gantt Diagram
        dateFormat  YYYY-MM-DD
        section Section
        A task           :a1, 2014-01-01, 30d
        Another task     :after a1  , 20d
        section Another
        Task in sec      :2014-01-12  , 12d
        another task      : 24d`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GanttChart" /* GanttChart */,
    description: "rich gantt chart",
    content: `gantt
        dateFormat  YYYY-MM-DD
        title       Adding GANTT diagram functionality to mermaid
        excludes    weekends
    
        section A section
        Completed task            :done,    des1, 2014-01-06,2014-01-08
        Active task               :active,  des2, 2014-01-09, 3d
        Future task               :         des3, after des2, 5d
        Future task2              :         des4, after des3, 5d
    
        section Critical tasks
        Completed task in the critical line :crit, done, 2014-01-06,24h
        Implement parser and jison          :crit, done, after des1, 2d
        Create tests for parser             :crit, active, 3d
        Future task in critical line        :crit, 5d
        Create tests for renderer           :2d
        Add to mermaid                      :1d
        Functionality added                 :milestone, 2014-01-25, 0d
    
        section Documentation
        Describe gantt syntax               :active, a1, after des1, 3d
        Add gantt diagram to demo page      :after a1  , 20h
        Add another diagram to demo page    :doc1, after a1  , 48h
    
        section Last section
        Describe gantt syntax               :after doc1, 3d
        Add gantt diagram to demo page      :20h
        Add another diagram to demo page    :48h`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GanttChart" /* GanttChart */,
    description: "milestones example",
    content: `gantt
        dateFormat HH:mm
        axisFormat %H:%M
        Initial milestone : milestone, m1, 17:49,2min
        taska2 : 10min
        taska3 : 5min
        Final milestone : milestone, m2, 18:14, 2min`,
    sortingOrder: 2,
    isPinned: false
  }
];

// src/elements/gitGraph.ts
var gitGraphElements = [
  {
    id: crypto.randomUUID(),
    category: "GitGraph" /* GitGraph */,
    description: "simple git graph",
    content: `gitGraph
        commit
        commit
        branch develop
        checkout develop
        commit
        commit
        checkout main
        merge develop
        commit
        commit`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GitGraph" /* GitGraph */,
    description: "tagged commit",
    content: `commit id: "Normal" tag: "v1.0.0"`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GitGraph" /* GitGraph */,
    description: "reverse commit",
    content: `commit id: "Reverse" type: REVERSE`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GitGraph" /* GitGraph */,
    description: "highlighted commit",
    content: `commit id: "Highlight" type: HIGHLIGHT`,
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GitGraph" /* GitGraph */,
    description: "reverse commit",
    content: `commit id: "Reverse" type: REVERSE`,
    sortingOrder: 4,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "GitGraph" /* GitGraph */,
    description: "git graph with cherry-pick",
    content: `gitGraph
        commit id: "ZERO"
        branch develop
        commit id:"A"
        checkout main
        commit id:"ONE"
        checkout develop
        commit id:"B"
        checkout main
        commit id:"TWO"
        cherry-pick id:"A"
        commit id:"THREE"
        checkout develop
        commit id:"C"`,
    sortingOrder: 5,
    isPinned: false
  }
];

// src/elements/kanban.ts
var kanbanElements = [
  {
    id: crypto.randomUUID(),
    category: "Kanban" /* Kanban */,
    description: "a sample kanban diagram",
    content: `kanban
  Todo
    [Create Documentation]
    docs[Create Blog about the new diagram]
  [In progress]
    id6[Create renderer so that it works in all cases. We also add som extra text here for testing purposes. And some more just for the extra flare.]
  id9[Ready for deploy]
    id8[Design grammar]@{ assigned: 'knsv' }
  id10[Ready for test]
    id4[Create parsing tests]@{ ticket: MC-2038, assigned: 'K.Sveidqvist', priority: 'High' }
    id66[last item]@{ priority: 'Very Low', assigned: 'knsv' }
  id11[Done]
    id5[define getData]
    id2[Title of diagram is more than 100 chars when user duplicates diagram with 100 char]@{ ticket: MC-2036, priority: 'Very High'}
    id3[Update DB function]@{ ticket: MC-2037, assigned: knsv, priority: 'High' }

  id12[Can't reproduce]
    id3[Weird flickering in Firefox]`,
    sortingOrder: 0,
    isPinned: false
  }
];

// src/elements/mindMap.ts
var mindMapElements = [
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "a simple mindmap",
    content: `mindmap
        Root
            A
              B
              C`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "square",
    content: `id[I am a square]`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "rounded square",
    content: `id(I am a rounded square)`,
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "circle",
    content: `id((I am a circle))`,
    sortingOrder: 4,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "bang",
    content: `id))I am a bang((`,
    sortingOrder: 5,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "cloud",
    content: `id)I am a cloud(`,
    sortingOrder: 6,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "hexagon",
    content: `id{{I am a hexagon}}`,
    sortingOrder: 7,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "default",
    content: `I am the default shape`,
    sortingOrder: 8,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Mindmap" /* Mindmap */,
    description: "sample mindmap",
    content: `mindmap
        root((mindmap))
          Origins
            Long history
            Popularisation
              British popular psychology author Tony Buzan
          Research
            On effectiveness<br/>and features
            On Automatic creation
              Uses
                  Creative techniques
                  Strategic planning
                  Argument mapping
          Tools
            Pen and paper
            Mermaid`,
    sortingOrder: 9,
    isPinned: false
  }
];

// src/elements/packet.ts
var packetElements = [
  {
    id: crypto.randomUUID(),
    category: "Packet" /* Packet */,
    description: "a sample packet diagram",
    content: `packet-beta
title UDP Packet
0-15: "Source Port"
16-31: "Destination Port"
32-47: "Length"
48-63: "Checksum"
64-95: "Data (variable length)"
`,
    sortingOrder: 0,
    isPinned: false
  }
];

// src/elements/pieChart.ts
var pieChartElements = [
  {
    id: crypto.randomUUID(),
    category: "PieChart" /* PieChart */,
    description: "sample pie chart",
    content: `pie title /r/obsidianmd posts by type
        "Graphs" : 85
        "Dashboards" : 14
        "Tips" : 1`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "PieChart" /* PieChart */,
    description: "sample pie chart with values shown in legend",
    content: `pie showData title /r/obsidianmd posts by type
        "Graphs" : 85
        "Dashboards" : 14
        "Tips" : 1`,
    sortingOrder: 1,
    isPinned: false
  }
];

// src/elements/quadrant.ts
var quadrantElements = [
  {
    id: crypto.randomUUID(),
    category: "QuadrantChart" /* QuadrantChart */,
    description: "sample quadrant chart",
    content: `quadrantChart
		title Reach and engagement of campaigns
		x-axis Low Reach --> High Reach
		y-axis Low Engagement --> High Engagement
		quadrant-1 We should expand
		quadrant-2 Need to promote
		quadrant-3 Re-evaluate
		quadrant-4 May be improved
		Campaign A: [0.3, 0.6]
		Campaign B: [0.45, 0.23]
		Campaign C: [0.57, 0.69]
		Campaign D: [0.78, 0.34]
		Campaign E: [0.40, 0.34]
		Campaign F: [0.35, 0.78]`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "QuadrantChart" /* QuadrantChart */,
    description: "themed quadrant chart",
    content: `%%{init: {"quadrantChart": {"chartWidth": 400, "chartHeight": 400}, "themeVariables": {"quadrant1TextFill": "#ff0000"} }}%%
		quadrantChart
		  x-axis Urgent --> Not Urgent
		  y-axis Not Important --> "Important \u2764"
		  quadrant-1 Plan
		  quadrant-2 Do
		  quadrant-3 Delegate
		  quadrant-4 Delete`,
    sortingOrder: 1,
    isPinned: false
  }
];

// src/elements/requirementDiagram.ts
var requirementDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "RequirementDiagram" /* RequirementDiagram */,
    description: "sample requirements diagram",
    content: `    requirementDiagram

        requirement test_req {
        id: 1
        text: the test text.
        risk: high
        verifymethod: test
        }
    
        element test_entity {
        type: simulation
        }
    
        test_entity - satisfies -> test_req`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "RequirementDiagram" /* RequirementDiagram */,
    description: "sample requirements diagram",
    content: `element customElement {
            type: customType
            docref: customDocRef
        }`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "RequirementDiagram" /* RequirementDiagram */,
    description: "a requirement with high risk",
    content: `functionalRequirement myReq {
            id: reqId
            text: someText
            risk: High
            verifymethod: analysis
        }`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "RequirementDiagram" /* RequirementDiagram */,
    description: "sample requirements diagram",
    content: `interfaceRequirement myReq2 {
            id: reqId
            text: someText
            risk: Medium
            verifymethod: demonstration
        }`,
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "RequirementDiagram" /* RequirementDiagram */,
    description: "sample requirements diagram",
    content: `designConstraint myReq3 {
            id: reqId
            text: someText
            risk: Low
            verifymethod: test
        }`,
    sortingOrder: 4,
    isPinned: false
  }
];

// src/elements/sankeyDiagram.ts
var sankeyDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "SankeyDiagram" /* SankeyDiagram */,
    description: "",
    content: `sankey-beta
        %% source,target,value
        Electricity grid,Over generation / exports,104.453
        Electricity grid,Heating and cooling - homes,113.726
        Electricity grid,H2 conversion,27.14`,
    sortingOrder: 0,
    isPinned: false
  }
];

// src/elements/sequenceDiagram.ts
var sequenceDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "SequenceDiagram" /* SequenceDiagram */,
    description: "a simple sequence diagram",
    content: `sequenceDiagram
Alice->>John: Hello John, how are you?
John-->>Alice: Great!
Alice-)John: See you later!`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "SequenceDiagram" /* SequenceDiagram */,
    description: "a simple sequence diagram with actors",
    content: `sequenceDiagram
actor Alice
actor John
Alice->>John: Hello John, how are you?
John-->>Alice: Great!
Alice-)John: See you later!`,
    sortingOrder: 1,
    isPinned: false
  }
];

// src/elements/stateDiagram.ts
var stateDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "a sample state diagram",
    content: `stateDiagram-v2
        [*] --> Still
        Still --> [*]
    
        Still --> Moving
        Moving --> Still
        Moving --> Crash
        Crash --> [*]`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "a sample state diagram with left-to-right direction",
    content: `stateDiagram-v2
        direction LR
        [*] --> Still
        Still --> [*]
    
        Still --> Moving
        Moving --> Still
        Moving --> Crash
        Crash --> [*]`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "node with description",
    content: `s2 : This is a state description`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "a transition",
    content: `s1 --> s2`,
    sortingOrder: 3,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "a transition with label",
    content: `s1 --> s2: A transition`,
    sortingOrder: 4,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "composite state",
    content: `
        [*] --> First
        state First {
            [*] --> second
            second --> [*]
        }`,
    sortingOrder: 5,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "diagram with choice",
    content: `stateDiagram-v2
        state if_state <<choice>>
        [*] --> IsPositive
        IsPositive --> if_state
        if_state --> False: if n < 0
        if_state --> True : if n >= 0`,
    sortingOrder: 6,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "diagram with fork",
    content: `stateDiagram-v2
        state fork_state <<fork>>
          [*] --> fork_state
          fork_state --> State2
          fork_state --> State3
    
          state join_state <<join>>
          State2 --> join_state
          State3 --> join_state
          join_state --> State4
          State4 --> [*]`,
    sortingOrder: 7,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "StateDiagram" /* StateDiagram */,
    description: "a diagram with concurrency",
    content: `stateDiagram-v2
        [*] --> Active
    
        state Active {
            [*] --> NumLockOff
            NumLockOff --> NumLockOn : EvNumLockPressed
            NumLockOn --> NumLockOff : EvNumLockPressed
            --
            [*] --> CapsLockOff
            CapsLockOff --> CapsLockOn : EvCapsLockPressed
            CapsLockOn --> CapsLockOff : EvCapsLockPressed
            --
            [*] --> ScrollLockOff
            ScrollLockOff --> ScrollLockOn : EvScrollLockPressed
            ScrollLockOn --> ScrollLockOff : EvScrollLockPressed
        }`,
    sortingOrder: 8,
    isPinned: false
  }
];

// src/elements/timeline.ts
var timelineElements = [
  {
    id: crypto.randomUUID(),
    category: "Timeline" /* Timeline */,
    description: "sample timeline",
    content: `timeline
		title History of Social Media Platform
		2002 : LinkedIn
		2004 : Facebook
			 : Google
		2005 : Youtube
		2006 : Twitter`,
    sortingOrder: 1,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Timeline" /* Timeline */,
    description: "timeline with grouping",
    content: `timeline
		title Timeline of Industrial Revolution
		section 17th-20th century
			Industry 1.0 : Machinery, Water power, Steam <br>power
			Industry 2.0 : Electricity, Internal combustion engine, Mass production
			Industry 3.0 : Electronics, Computers, Automation
		section 21st century
			Industry 4.0 : Internet, Robotics, Internet of Things
			Industry 5.0 : Artificial intelligence, Big data,3D printing`,
    sortingOrder: 2,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "Timeline" /* Timeline */,
    description: "timeline with Forest theme. see the docs for additional themes",
    content: `%%{init: { 'logLevel': 'debug', 'theme': 'forest' } }%%
		timeline
			title History of Social Media Platform
			  2002 : LinkedIn
			  2004 : Facebook : Google
			  2005 : Youtube
			  2006 : Twitter
			  2007 : Tumblr
			  2008 : Instagram
			  2010 : Pinterest`,
    sortingOrder: 3,
    isPinned: false
  }
];

// src/elements/userJourneyDiagram.ts
var userJourneyDiagramElements = [
  {
    id: crypto.randomUUID(),
    category: "UserJourneyDiagram" /* UserJourneyDiagram */,
    description: "a sample user journey diagram",
    content: `journey
        title My working day
        section Go to work
          Make tea: 5: Me
          Go upstairs: 3: Me
          Do work: 1: Me, Cat
        section Go home
          Go downstairs: 5: Me
          Sit down: 5: Me`,
    sortingOrder: 0,
    isPinned: false
  },
  {
    id: crypto.randomUUID(),
    category: "UserJourneyDiagram" /* UserJourneyDiagram */,
    description: "a step in user journey",
    content: `      Step Title: 5: ActorName`,
    sortingOrder: 1,
    isPinned: false
  }
];

// src/elements/xyChart.ts
var xyChartElements = [
  {
    id: crypto.randomUUID(),
    category: "XyChart" /* XyChart */,
    description: "a sample XYChart diagram",
    content: `xychart-beta
        title "Sales Revenue"
        x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]
        y-axis "Revenue (in $)" 4000 --> 11000
        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]
        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]`,
    sortingOrder: 0,
    isPinned: false
  }
];

// src/elements/defaultElements.ts
var defaultElements = [
  ...flowchartElements,
  ...sequenceDiagramElements,
  ...classDiagramElements,
  ...stateDiagramElements,
  ...entityRelationshipDiagramElements,
  ...userJourneyDiagramElements,
  ...ganttChartElements,
  ...pieChartElements,
  ...requirementDiagramElements,
  ...gitGraphElements,
  ...mindMapElements,
  ...timelineElements,
  ...quadrantElements,
  ...c4DiagramElements,
  ...sankeyDiagramElements,
  ...xyChartElements,
  ...packetElements,
  ...kanbanElements,
  ...blockDiagramElements,
  ...architectureElements
];

// src/core/elementService.ts
var wrappingsForElementCategories = {
  Flowchart: { defaultWrapping: "flowchart LR", wrappings: ["flowchart LR", "flowchart TD"] },
  SequenceDiagram: { defaultWrapping: "sequenceDiagram", wrappings: null },
  ClassDiagram: { defaultWrapping: "classDiagram", wrappings: null },
  StateDiagram: { defaultWrapping: "stateDiagram-v2", wrappings: null },
  EntityRelationshipDiagram: { defaultWrapping: "erDiagram", wrappings: null },
  UserJourneyDiagram: { defaultWrapping: "journey", wrappings: null },
  GanttChart: { defaultWrapping: "gantt", wrappings: null },
  PieChart: { defaultWrapping: "pie", wrappings: null },
  RequirementDiagram: { defaultWrapping: "requirementDiagram", wrappings: null },
  GitGraph: { defaultWrapping: "gitGraph", wrappings: null },
  Mindmap: { defaultWrapping: "mindmap", wrappings: ["mindmap"] },
  Timeline: { defaultWrapping: "timeline", wrappings: null },
  QuadrantChart: { defaultWrapping: "quadrantChart", wrappings: null },
  C4Diagram: { defaultWrapping: "C4Context", wrappings: null },
  SankeyDiagram: { defaultWrapping: "sankey-beta", wrappings: null },
  XyChart: { defaultWrapping: "xychart-beta", wrappings: null },
  Packet: { defaultWrapping: "packet-beta", wrappings: null },
  Kanban: { defaultWrapping: "kanban", wrappings: null },
  Block: { defaultWrapping: "block-beta", wrappings: null },
  Architecture: { defaultWrapping: "architecture-beta", wrappings: null }
};
var MermaidElementService = class {
  static DefaultElements() {
    return defaultElements;
  }
  saveElement(element, plugin) {
    const elementExists = plugin.settings.elements.some((el) => el.id === element.id);
    if (elementExists) {
      plugin.settings.elements.forEach((el) => {
        if (el.id === element.id) {
          el = element;
        }
      });
    } else {
      this.fixSortOrder(element, plugin);
      plugin.settings.elements.push(element);
    }
    plugin.saveSettings();
  }
  fixSortOrder(element, plugin) {
    const elementsFromSameCategory = plugin.settings.elements.filter((element2) => element2.category === element2.category);
    if (elementsFromSameCategory.some((element2) => element2.sortingOrder === element2.sortingOrder)) {
      element.sortingOrder = elementsFromSameCategory.length;
    }
  }
  getSampleDiagram(category) {
    return this.wrapForPastingIntoEditor(this.wrapWithMermaidBlock(sampleDiagrams[category]));
  }
  wrapForPastingIntoEditor(text) {
    return `${text}
`;
  }
  wrapWithMermaidBlock(text) {
    return `\`\`\`mermaid
${text}
\`\`\``;
  }
  wrapAsCompleteDiagram(element) {
    const wrapping = wrappingsForElementCategories[element.category];
    return (wrapping.wrappings ? wrapping.wrappings.some((w) => element.content.contains(w)) : element.content.contains(wrapping.defaultWrapping)) ? element.content : wrapping.defaultWrapping + "\n" + element.content;
  }
};

// src/core/textEditorService.ts
var NoActiveCursorError = class extends Error {
  constructor() {
    super();
    this.message = "Mermaid Tools: Error getting cursor position. Make sure you are in editing mode and have an active cursor in file content.";
  }
};
var TextEditorService = class {
  constructor() {
    this._elementService = new MermaidElementService();
  }
  insertTextAtCursor(editor, content) {
    if (!editor)
      throw new NoActiveCursorError();
    content = this._elementService.wrapForPastingIntoEditor(content);
    const cursor = editor.getCursor();
    editor.replaceRange(content, cursor);
    editor.setCursor(content.length);
    editor.focus();
  }
};

// src/settings/settings.ts
var MermaidPluginSettings = class {
  constructor() {
    this.categories = ElementCategory;
  }
  static DefaultSettings() {
    let settings = new MermaidPluginSettings();
    settings.elements = defaultElements;
    settings.selectedCategory = "Flowchart" /* Flowchart */;
    return settings;
  }
};

// src/trident-icon.ts
var import_obsidian = require("obsidian");
function addTridentIcon() {
  (0, import_obsidian.addIcon)(TRIDENT_ICON_NAME, getSanitizedSvg());
}
function getSanitizedSvg() {
  var sanitized = (0, import_obsidian.sanitizeHTMLToDom)(tridentIcon);
  var tempContainer = createDiv();
  tempContainer.appendChild(sanitized);
  return tempContainer.innerHTML;
}
var tridentIcon = `<svg width="100" height="100" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M63.3876 30.1639C62.8776 29.6541 62.1414 29.4434 61.439 29.6051L46.6574 32.5601C45.9179 
32.7318 45.3279 33.2907 45.1192 34.0208C44.9089 34.7506 45.113 35.5373 45.6499 36.0742L47.1282 
37.552L36.7814 47.895C35.965 48.7111 34.6415 48.7111 33.8252 47.895L29.3908 43.4622L50.0846 22.7759L51.5629 
24.2537C52.0998 24.7904 52.8871 24.9944 53.6169 24.7842C54.3473 24.5753 54.9064 23.9858 55.0782 
23.2466L58.0341 8.4704C58.1958 7.76815 57.9851 7.03228 57.4751 6.52252C56.965 6.01265 56.229 5.80202 
55.5265 5.96365L40.7453 8.91853C40.0057 9.09028 39.4158 9.64916 39.2071 10.3793C38.9967 11.109 39.2008 
11.8958 39.7378 12.4327L41.2161 13.9104L20.5223 34.5966L16.0879 30.1638C15.2716 29.3477 15.2716 28.0247 
16.0879 27.2087L26.4347 16.8657L27.913 18.3434C28.45 18.8802 29.2373 19.0842 29.967 18.8739C30.6974 18.6653 
31.2565 18.0755 31.4283 17.3363L34.3843 2.56014C34.546 1.85789 34.3353 1.12202 33.8252 0.612265C33.3151 0.102514 
32.579 -0.108237 31.8765 0.0533888L17.0953 3.00839C16.3557 3.18014 15.7658 3.73902 15.5571 4.46915C15.3467 5.1989 
15.5508 5.98565 16.0878 6.5224L17.5661 8.00015L7.21925 18.3432C1.50488 24.0554 1.50488 33.3169 7.21925 39.0295L11.6536 
43.4622L0.610679 54.524C-0.204122 55.3402 -0.203497 56.6621 0.612179 57.4776L6.52461 63.388C7.34154 64.2046 8.66628 
64.2039 9.48233 63.3865L20.522 52.3277L24.9564 56.7605C30.6708 62.4727 39.9358 62.4727 45.6501 56.7605L55.9969 46.4175L57.4752 
47.8952C58.0121 48.432 58.7994 48.636 59.5292 48.4257C60.2596 48.2171 60.8187 47.6273 60.9905 46.8881L63.9464 32.1119C64.1084 
31.4097 63.8977 30.6738 63.3876 30.1639ZM57.6701 42.1787L55.9975 40.5068L42.6944 53.8051C38.6127 57.8855 31.9948 57.8852 27.9131 
53.8051L20.5223 46.4173L17.5661 49.3725L8.00441 58.9569L5.04819 56.0017L14.6098 46.4173L17.5661 43.4622L10.1755 36.0743C6.09383 
31.9942 6.09383 25.3787 10.1755 21.2986L23.4786 8.00028L21.8053 6.3274L29.6834 4.75302L28.1077 12.6273L26.4352 10.9554L13.1321 
24.2537C10.6829 26.7019 10.6829 30.6711 13.1321 33.1192L20.5227 40.5071L47.1287 13.9108L45.4554 12.2378L53.3335 10.6635L51.7578 
18.5378L50.0853 16.8659L23.4786 43.462L30.8692 50.8499C33.3184 53.2979 37.289 53.2979 39.738 50.8499L53.0412 37.5516L51.3678 
35.8786L59.246 34.3042L57.6701 42.1787Z" fill="currentColor"/>
</svg>`;

// src/ui/settingsTab.ts
var import_obsidian3 = require("obsidian");

// src/ui/editMermaidElementModal.ts
var import_obsidian2 = require("obsidian");
var EditMermaidElementModal = class extends import_obsidian2.Modal {
  constructor(app, _plugin, _mermaid, _element) {
    super(app);
    this._plugin = _plugin;
    this._mermaid = _mermaid;
    this._element = _element;
  }
  async onOpen() {
    let { contentEl } = this;
    contentEl.addClass("mermaid-tools-edit-element-modal");
    contentEl.createEl("h2", { text: "Edit element" });
    let renderContainerEl = contentEl.createDiv();
    let renderEl = renderContainerEl.createEl("pre", { text: "rendered diagram" });
    if (!this._mermaid)
      this._mermaid = await (0, import_obsidian2.loadMermaid)();
    renderEl.id = "mermaid-edit-element-modal";
    let elementCategoryContainerEl = contentEl.createDiv();
    elementCategoryContainerEl.createEl("label", { text: "Category" });
    let elementCategoryEl = elementCategoryContainerEl.createEl("select");
    for (let category in ElementCategory) {
      let option = elementCategoryEl.createEl("option", { text: category });
      option.value = category;
    }
    elementCategoryEl.value = this._element.category;
    elementCategoryEl.onchange = (e) => {
      this._element.category = elementCategoryEl.value;
    };
    let elementDescriptionContainerEl = contentEl.createDiv();
    elementDescriptionContainerEl.createEl("label", { text: "Description" });
    let elementDescriptionEl = elementDescriptionContainerEl.createEl("input", { value: this._element.description, type: "text" });
    elementDescriptionEl.style.minWidth = "50%";
    elementDescriptionEl.onchange = (e) => {
      this._element.description = elementDescriptionEl.value;
    };
    let elementContentContainerEl = contentEl.createDiv();
    elementContentContainerEl.createEl("label", { text: "Content" });
    let elementContentEl = elementContentContainerEl.createEl("textarea", { text: this._element.content });
    elementContentEl.style.height = "200px";
    elementContentEl.style.width = "100%";
    elementContentEl.onchange = async (e) => {
      this._element.content = elementContentEl.value;
      let { svg: svg2 } = await this._mermaid.render(renderEl.id, this._plugin._mermaidElementService.wrapAsCompleteDiagram(this._element));
      renderEl.innerHTML = svg2;
      renderContainerEl.appendChild(renderEl);
    };
    let saveButtonEl = contentEl.createEl("button", { text: "Save" });
    saveButtonEl.onclick = (e) => {
      this.save();
    };
    let { svg } = await this._mermaid.render(renderEl.id, this._plugin._mermaidElementService.wrapAsCompleteDiagram(this._element));
    renderEl.innerHTML = svg;
    renderContainerEl.appendChild(renderEl);
  }
  save() {
    this._plugin._mermaidElementService.saveElement(this._element, this._plugin);
    this.close();
  }
};

// src/ui/settingsTab.ts
var MermaidToolsSettingsTab = class extends import_obsidian3.PluginSettingTab {
  constructor(_app, _plugin) {
    super(_app, _plugin);
    this._app = _app;
    this._plugin = _plugin;
  }
  async display() {
    await renderElements(this.containerEl, this._plugin);
  }
};
async function renderElements(containerEl, plugin) {
  let mermaid = await (0, import_obsidian3.loadMermaid)();
  containerEl.empty();
  containerEl.createEl("h1", { text: "Mermaid Tools Settings" });
  containerEl.createEl("h2", { text: "Manage elements" });
  createAddButton(containerEl, plugin);
  for (let category in ElementCategory) {
    renderElementCategory(category, plugin, containerEl, mermaid);
  }
  ;
}
function renderElementCategory(category, plugin, parentEl, mermaid) {
  let copy = [...plugin.settings.elements];
  let elements = copy.filter((e) => e.category === category);
  let containerEl = document.getElementById(category + "-container");
  let isFirstRender = !containerEl;
  containerEl != null ? containerEl : containerEl = parentEl.createDiv();
  containerEl.id = category + "-container";
  containerEl.innerHTML = "";
  let header = containerEl.createEl("h3", { text: category });
  header.addClass("mermaid-tools-element-category-header");
  let elementsContainerEl = containerEl.createDiv();
  elementsContainerEl.addClass("mermaid-tools-element-category-container");
  header.removeClass("collapsed");
  elementsContainerEl.hidden = false;
  if (isFirstRender) {
    header.addClass("collapsed");
    elementsContainerEl.hidden = true;
  }
  header.onClickEvent(() => {
    header.classList.toggle("collapsed");
    elementsContainerEl.hidden = !elementsContainerEl.hidden;
  });
  elements.sort((a, b) => a.sortingOrder - b.sortingOrder).forEach(async (element, index) => {
    let settingContainer = elementsContainerEl.createDiv("mermaid-tools-element-container");
    const setting = new import_obsidian3.Setting(settingContainer);
    setting.setName(element.description);
    setting.addExtraButton((cb) => {
      cb.setIcon("edit").setTooltip("edit element").onClick(() => {
        let modal = new EditMermaidElementModal(plugin.app, plugin, mermaid, element);
        modal.open();
        modal.onClose = () => {
          renderElementCategory(category, plugin, parentEl, mermaid);
        };
      });
    });
    setting.addExtraButton((cb) => {
      cb.setIcon("copy").setTooltip("create a duplicate of this element").onClick(() => {
        let duplicate = {
          id: crypto.randomUUID(),
          category: element.category,
          description: element.description + " (copy)",
          content: element.content,
          sortingOrder: plugin.settings.elements.filter((el) => el.category === element.category).length,
          isPinned: element.isPinned
        };
        plugin._mermaidElementService.saveElement(duplicate, plugin);
        plugin.saveSettings();
        renderElementCategory(category, plugin, parentEl, mermaid);
      });
    });
    setting.addExtraButton((cb) => {
      cb.setIcon("arrow-up").setTooltip("move element up in the sidebar").onClick(() => {
        if (index > 0) {
          const temp = elements[index - 1].sortingOrder;
          elements[index - 1].sortingOrder = element.sortingOrder;
          element.sortingOrder = temp;
          plugin.settings.elements = plugin.settings.elements.filter((el) => el.category !== category).concat(elements);
          plugin.saveSettings();
          renderElementCategory(category, plugin, parentEl, mermaid);
        }
      });
    });
    setting.addExtraButton((cb) => {
      cb.setIcon("arrow-down").setTooltip("move element down in the sidebar").onClick(() => {
        if (index < elements.length - 1) {
          const temp = elements[index + 1].sortingOrder;
          elements[index + 1].sortingOrder = element.sortingOrder;
          element.sortingOrder = temp;
          plugin.settings.elements = plugin.settings.elements.filter((el) => el.category !== category).concat(elements);
          plugin.saveSettings();
          renderElementCategory(category, plugin, parentEl, mermaid);
        }
      });
    });
    setting.addExtraButton((cb) => {
      cb.setIcon("trash-2").setTooltip("delete element").onClick(() => {
        plugin.settings.elements = plugin.settings.elements.filter((e) => e.id !== element.id);
        plugin.saveSettings();
        renderElementCategory(category, plugin, parentEl, mermaid);
      });
    });
  });
}
function createAddButton(parentEl, plugin) {
  const addButton = parentEl.createEl("button", { text: "Add" });
  addButton.innerHTML = "Add an element";
  addButton.onclick = () => {
    let newElement = {
      id: crypto.randomUUID(),
      description: "New element",
      content: `flowchart TD
Start --> Stop`,
      category: "Flowchart" /* Flowchart */,
      sortingOrder: 0,
      isPinned: false
    };
    let modal = new EditMermaidElementModal(plugin.app, plugin, null, newElement);
    modal.open();
    modal.onClose = () => {
      renderElementCategory(modal._element.category, plugin, parentEl, null);
    };
  };
}

// src/ui/toolbarView/mermaidToolbarView.ts
var import_obsidian5 = require("obsidian");

// src/ui/toolbarView/viewHelpers.ts
var import_obsidian4 = require("obsidian");
var TOOLBAR_ELEMENT_CLASS_NAME = "mermaid-toolbar-element";
var TOOLBAR_ELEMENTS_CONTAINER_CLASS_NAME = "mermaid-toolbar-elements-container";
var TOOLBAR_ELEMENTS_CONTAINER_ID = "mermaid-toolbar-elements-container-id";
async function createMermaidToolbar(topRowButtons, items, selectedCategory, onCategoryChanged, onElementClick) {
  const container = document.createElement("div");
  const topRow = container.createDiv();
  topRow.addClass("mermaid-toolbar-top-row");
  const elementsContainer = container.createDiv();
  elementsContainer.addClass(TOOLBAR_ELEMENTS_CONTAINER_CLASS_NAME);
  elementsContainer.setAttr("id", TOOLBAR_ELEMENTS_CONTAINER_ID);
  createDropdown(topRow, elementsContainer, items, selectedCategory, onCategoryChanged, onElementClick);
  createTopRowBtns(topRow, topRowButtons);
  await recreateElementsSection(elementsContainer, selectedCategory, items, onElementClick);
  return container;
}
function createTopRowBtns(parentEl, buttons) {
  buttons.forEach((btn) => {
    const b = new import_obsidian4.ButtonComponent(parentEl).setClass("clickable-icon").setIcon(btn.iconName).setTooltip(btn.tooltip).onClick(btn.callback);
  });
}
function createDropdown(parentEl, elementsContainer, items, selectedCategory, onSelectionChanged, onElClick) {
  const categories = Object.keys(ElementCategory);
  const dropdown = new import_obsidian4.DropdownComponent(parentEl);
  categories.forEach((c) => {
    dropdown.addOption(c, c);
  });
  dropdown.setValue(selectedCategory);
  dropdown.onChange((val) => {
    onSelectionChanged(val);
    recreateElementsSection(elementsContainer, val, items, onElClick);
  });
}
async function recreateElementsSection(sectionContainer, category, items, onElClick) {
  sectionContainer.innerHTML = "";
  const elemService = new MermaidElementService();
  const mermaid = await (0, import_obsidian4.loadMermaid)();
  const filteredSortedItems = items.filter((i) => i.category == category).sort((a, b) => a.sortingOrder - b.sortingOrder);
  filteredSortedItems.forEach(async (elem, index) => {
    const el = createToolbarElement(sectionContainer);
    el.id = `mermaid-toolbar-element-${elem.category}-${index}`;
    const diagram = elemService.wrapAsCompleteDiagram(elem);
    console.log(mermaid.detectType(diagram));
    const { svg } = await mermaid.render(el.id, diagram);
    el.title = elem.description;
    el.innerHTML = svg;
    el.onclick = (e) => onElClick(elem.content);
    sectionContainer.appendChild(el);
  });
}
function createToolbarElement(parentEl) {
  const itemEl = parentEl.createEl("pre");
  itemEl.addClass(TOOLBAR_ELEMENT_CLASS_NAME);
  return itemEl;
}

// src/ui/toolbarView/mermaidToolbarButtons.ts
var MermaidToolbarButton = class {
  constructor(tooltip, iconName, callback) {
    this.tooltip = tooltip;
    this.iconName = iconName;
    this.callback = callback;
  }
};

// src/ui/toolbarView/mermaidToolbarView.ts
var _MermaidToolbarView = class extends import_obsidian5.ItemView {
  constructor(leaf, plugin) {
    super(leaf);
    this.topRowButtons = [
      new MermaidToolbarButton("insert Mermaid code block with sample diagram", "code-2", () => this.insertTextAtCursor(this._plugin._mermaidElementService.getSampleDiagram(this._plugin.settings.selectedCategory))),
      new MermaidToolbarButton("open Mermaid.js documentation web page", "external-link", () => window.open("https://mermaid.js.org/intro/")),
      new MermaidToolbarButton("open settings", "settings", () => {
        this.app.setting.open();
        this.app.setting.openTabById("mermaid-tools");
      })
    ];
    this._plugin = plugin;
    this.items = plugin.settings.elements;
    this.containerEl.children[1].addClass("mermaid-toolbar-container");
  }
  async onOpen() {
    await this.recreateToolbar(this._plugin.settings.selectedCategory);
  }
  async onClose() {
  }
  async recreateToolbar(selectedCategory) {
    const container = this.containerEl.children[1];
    container.empty();
    let toolbarElement = await createMermaidToolbar(this.topRowButtons, this.items, selectedCategory, async (newCat) => {
      this._plugin.settings.selectedCategory = newCat;
      this._plugin.saveSettings();
      await this.recreateToolbar(this._plugin.settings.selectedCategory);
    }, (text) => this.insertTextAtCursor(text));
    container.appendChild(toolbarElement);
  }
  insertTextAtCursor(text) {
    this._plugin.insertTextAtCursor(text);
  }
  getViewType() {
    return _MermaidToolbarView.VIEW_TYPE;
  }
  getDisplayText() {
    return _MermaidToolbarView.VIEW_DESCRIPTION;
  }
  getIcon() {
    return TRIDENT_ICON_NAME;
  }
};
var MermaidToolbarView = _MermaidToolbarView;
MermaidToolbarView.VIEW_TYPE = "mermaid-toolbar-view";
MermaidToolbarView.VIEW_DESCRIPTION = "Mermaid Toolbar";

// main.ts
var TRIDENT_ICON_NAME = "trident-custom";
var MermaidPlugin = class extends import_obsidian6.Plugin {
  constructor() {
    super(...arguments);
    this._mermaidElementService = new MermaidElementService();
    this._textEditorService = new TextEditorService();
  }
  async onload() {
    await this.loadSettings();
    addTridentIcon();
    this.registerView(MermaidToolbarView.VIEW_TYPE, (leaf) => new MermaidToolbarView(leaf, this));
    this.app.workspace.on("active-leaf-change", (leaf) => {
      var _a, _b;
      this.activeEditor = (_b = (_a = this.app.workspace.activeEditor) == null ? void 0 : _a.editor) != null ? _b : this.activeEditor;
    });
    this.addRibbonIcon(TRIDENT_ICON_NAME, "Open Mermaid Toolbar", () => {
      this.activateView();
    });
    this.addCommand({
      id: "open-toolbar",
      name: "Open Toolbar View",
      callback: () => {
        this.activateView();
      }
    });
    this.addSettingTab(new MermaidToolsSettingsTab(this.app, this));
  }
  async onunload() {
    this.app.workspace.detachLeavesOfType(MermaidToolbarView.VIEW_TYPE);
  }
  async loadSettings() {
    this.settings = Object.assign({}, MermaidPluginSettings.DefaultSettings(), await this.loadData());
    this.addNewCategories();
  }
  addNewCategories() {
    if (!this.settings.elements.some((x) => x.category === "Mindmap" /* Mindmap */)) {
      this.settings.elements.push(...mindMapElements);
      console.log("[Mermaid Tools] added Mindmap elements");
    }
    if (!this.settings.elements.some((x) => x.category === "Timeline" /* Timeline */)) {
      this.settings.elements.push(...timelineElements);
      console.log("[Mermaid Tools] added Timeline elements");
    }
    if (!this.settings.elements.some((x) => x.category === "QuadrantChart" /* QuadrantChart */)) {
      this.settings.elements.push(...quadrantElements);
      console.log("[Mermaid Tools] added QuadrantChart elements");
    }
    if (!this.settings.elements.some((x) => x.category === "C4Diagram" /* C4Diagram */)) {
      this.settings.elements.push(...c4DiagramElements);
      console.log("[Mermaid Tools] added C4 diagram elements");
    }
    if (!this.settings.elements.some((x) => x.category === "Packet" /* Packet */)) {
      this.settings.elements.push(...packetElements);
      console.log("[Mermaid Tools] added Packet elements");
    }
    if (!this.settings.elements.some((x) => x.category === "Kanban" /* Kanban */)) {
      this.settings.elements.push(...kanbanElements);
      console.log("[Mermaid Tools] added Kanban elements");
    }
    if (!this.settings.elements.some((x) => x.category === "Block" /* Block */)) {
      this.settings.elements.push(...blockDiagramElements);
      console.log("[Mermaid Tools] added Block elements");
    }
    if (!this.settings.elements.some((x) => x.category === "Architecture" /* Architecture */)) {
      this.settings.elements.push(...architectureElements);
      console.log("[Mermaid Tools] added Architecture diagram elements");
    }
  }
  async saveSettings() {
    await this.saveData(this.settings);
    await this.activateView();
  }
  async activateView() {
    var _a;
    this.app.workspace.detachLeavesOfType(MermaidToolbarView.VIEW_TYPE);
    if (this.app.workspace === null)
      return;
    await ((_a = this.app.workspace.getRightLeaf(false)) == null ? void 0 : _a.setViewState({
      type: MermaidToolbarView.VIEW_TYPE,
      active: true
    }));
    this.app.workspace.revealLeaf(this.app.workspace.getLeavesOfType(MermaidToolbarView.VIEW_TYPE)[0]);
  }
  insertTextAtCursor(text) {
    this._textEditorService.insertTextAtCursor(this.activeEditor, text);
  }
};

/* nosourcemap */