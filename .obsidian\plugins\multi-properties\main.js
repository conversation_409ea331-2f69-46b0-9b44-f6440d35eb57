/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => MultiPropPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian6 = require("obsidian");

// src/AddPropModal.ts
var import_obsidian2 = require("obsidian");

// node_modules/svelte/src/runtime/internal/utils.js
function noop() {
}
function run(fn) {
  return fn();
}
function blank_object() {
  return /* @__PURE__ */ Object.create(null);
}
function run_all(fns) {
  fns.forEach(run);
}
function is_function(thing) {
  return typeof thing === "function";
}
function safe_not_equal(a, b) {
  return a != a ? b == b : a !== b || a && typeof a === "object" || typeof a === "function";
}
function is_empty(obj) {
  return Object.keys(obj).length === 0;
}

// node_modules/svelte/src/runtime/internal/globals.js
var globals = typeof window !== "undefined" ? window : typeof globalThis !== "undefined" ? globalThis : (
  // @ts-ignore Node typings have this
  global
);

// node_modules/svelte/src/runtime/internal/ResizeObserverSingleton.js
var ResizeObserverSingleton = class {
  /** @param {ResizeObserverOptions} options */
  constructor(options) {
    /**
     * @private
     * @readonly
     * @type {WeakMap<Element, import('./private.js').Listener>}
     */
    __publicField(this, "_listeners", "WeakMap" in globals ? /* @__PURE__ */ new WeakMap() : void 0);
    /**
     * @private
     * @type {ResizeObserver}
     */
    __publicField(this, "_observer");
    /** @type {ResizeObserverOptions} */
    __publicField(this, "options");
    this.options = options;
  }
  /**
   * @param {Element} element
   * @param {import('./private.js').Listener} listener
   * @returns {() => void}
   */
  observe(element2, listener) {
    this._listeners.set(element2, listener);
    this._getObserver().observe(element2, this.options);
    return () => {
      this._listeners.delete(element2);
      this._observer.unobserve(element2);
    };
  }
  /**
   * @private
   */
  _getObserver() {
    var _a;
    return (_a = this._observer) != null ? _a : this._observer = new ResizeObserver((entries) => {
      var _a2;
      for (const entry of entries) {
        ResizeObserverSingleton.entries.set(entry.target, entry);
        (_a2 = this._listeners.get(entry.target)) == null ? void 0 : _a2(entry);
      }
    });
  }
};
ResizeObserverSingleton.entries = "WeakMap" in globals ? /* @__PURE__ */ new WeakMap() : void 0;

// node_modules/svelte/src/runtime/internal/dom.js
var is_hydrating = false;
function start_hydrating() {
  is_hydrating = true;
}
function end_hydrating() {
  is_hydrating = false;
}
function append(target, node) {
  target.appendChild(node);
}
function append_styles(target, style_sheet_id, styles) {
  const append_styles_to = get_root_for_style(target);
  if (!append_styles_to.getElementById(style_sheet_id)) {
    const style = element("style");
    style.id = style_sheet_id;
    style.textContent = styles;
    append_stylesheet(append_styles_to, style);
  }
}
function get_root_for_style(node) {
  if (!node)
    return document;
  const root = node.getRootNode ? node.getRootNode() : node.ownerDocument;
  if (root && /** @type {ShadowRoot} */
  root.host) {
    return (
      /** @type {ShadowRoot} */
      root
    );
  }
  return node.ownerDocument;
}
function append_stylesheet(node, style) {
  append(
    /** @type {Document} */
    node.head || node,
    style
  );
  return style.sheet;
}
function insert(target, node, anchor) {
  target.insertBefore(node, anchor || null);
}
function detach(node) {
  if (node.parentNode) {
    node.parentNode.removeChild(node);
  }
}
function destroy_each(iterations, detaching) {
  for (let i = 0; i < iterations.length; i += 1) {
    if (iterations[i])
      iterations[i].d(detaching);
  }
}
function element(name) {
  return document.createElement(name);
}
function text(data) {
  return document.createTextNode(data);
}
function space() {
  return text(" ");
}
function empty() {
  return text("");
}
function listen(node, event, handler, options) {
  node.addEventListener(event, handler, options);
  return () => node.removeEventListener(event, handler, options);
}
function prevent_default(fn) {
  return function(event) {
    event.preventDefault();
    return fn.call(this, event);
  };
}
function attr(node, attribute, value) {
  if (value == null)
    node.removeAttribute(attribute);
  else if (node.getAttribute(attribute) !== value)
    node.setAttribute(attribute, value);
}
function children(element2) {
  return Array.from(element2.childNodes);
}
function set_data(text2, data) {
  data = "" + data;
  if (text2.data === data)
    return;
  text2.data = /** @type {string} */
  data;
}
function set_input_value(input, value) {
  input.value = value == null ? "" : value;
}
function select_option(select, value, mounting) {
  for (let i = 0; i < select.options.length; i += 1) {
    const option = select.options[i];
    if (option.__value === value) {
      option.selected = true;
      return;
    }
  }
  if (!mounting || value !== void 0) {
    select.selectedIndex = -1;
  }
}
function select_value(select) {
  const selected_option = select.querySelector(":checked");
  return selected_option && selected_option.__value;
}
function get_custom_elements_slots(element2) {
  const result = {};
  element2.childNodes.forEach(
    /** @param {Element} node */
    (node) => {
      result[node.slot || "default"] = true;
    }
  );
  return result;
}

// node_modules/svelte/src/runtime/internal/lifecycle.js
var current_component;
function set_current_component(component) {
  current_component = component;
}
function get_current_component() {
  if (!current_component)
    throw new Error("Function called outside component initialization");
  return current_component;
}
function onMount(fn) {
  get_current_component().$$.on_mount.push(fn);
}
function bubble(component, event) {
  const callbacks = component.$$.callbacks[event.type];
  if (callbacks) {
    callbacks.slice().forEach((fn) => fn.call(this, event));
  }
}

// node_modules/svelte/src/runtime/internal/scheduler.js
var dirty_components = [];
var binding_callbacks = [];
var render_callbacks = [];
var flush_callbacks = [];
var resolved_promise = /* @__PURE__ */ Promise.resolve();
var update_scheduled = false;
function schedule_update() {
  if (!update_scheduled) {
    update_scheduled = true;
    resolved_promise.then(flush);
  }
}
function tick() {
  schedule_update();
  return resolved_promise;
}
function add_render_callback(fn) {
  render_callbacks.push(fn);
}
function add_flush_callback(fn) {
  flush_callbacks.push(fn);
}
var seen_callbacks = /* @__PURE__ */ new Set();
var flushidx = 0;
function flush() {
  if (flushidx !== 0) {
    return;
  }
  const saved_component = current_component;
  do {
    try {
      while (flushidx < dirty_components.length) {
        const component = dirty_components[flushidx];
        flushidx++;
        set_current_component(component);
        update(component.$$);
      }
    } catch (e) {
      dirty_components.length = 0;
      flushidx = 0;
      throw e;
    }
    set_current_component(null);
    dirty_components.length = 0;
    flushidx = 0;
    while (binding_callbacks.length)
      binding_callbacks.pop()();
    for (let i = 0; i < render_callbacks.length; i += 1) {
      const callback = render_callbacks[i];
      if (!seen_callbacks.has(callback)) {
        seen_callbacks.add(callback);
        callback();
      }
    }
    render_callbacks.length = 0;
  } while (dirty_components.length);
  while (flush_callbacks.length) {
    flush_callbacks.pop()();
  }
  update_scheduled = false;
  seen_callbacks.clear();
  set_current_component(saved_component);
}
function update($$) {
  if ($$.fragment !== null) {
    $$.update();
    run_all($$.before_update);
    const dirty = $$.dirty;
    $$.dirty = [-1];
    $$.fragment && $$.fragment.p($$.ctx, dirty);
    $$.after_update.forEach(add_render_callback);
  }
}
function flush_render_callbacks(fns) {
  const filtered = [];
  const targets = [];
  render_callbacks.forEach((c) => fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c));
  targets.forEach((c) => c());
  render_callbacks = filtered;
}

// node_modules/svelte/src/runtime/internal/transitions.js
var outroing = /* @__PURE__ */ new Set();
var outros;
function group_outros() {
  outros = {
    r: 0,
    c: [],
    p: outros
    // parent group
  };
}
function check_outros() {
  if (!outros.r) {
    run_all(outros.c);
  }
  outros = outros.p;
}
function transition_in(block, local) {
  if (block && block.i) {
    outroing.delete(block);
    block.i(local);
  }
}
function transition_out(block, local, detach2, callback) {
  if (block && block.o) {
    if (outroing.has(block))
      return;
    outroing.add(block);
    outros.c.push(() => {
      outroing.delete(block);
      if (callback) {
        if (detach2)
          block.d(1);
        callback();
      }
    });
    block.o(local);
  } else if (callback) {
    callback();
  }
}

// node_modules/svelte/src/runtime/internal/each.js
function ensure_array_like(array_like_or_iterator) {
  return (array_like_or_iterator == null ? void 0 : array_like_or_iterator.length) !== void 0 ? array_like_or_iterator : Array.from(array_like_or_iterator);
}
function outro_and_destroy_block(block, lookup) {
  transition_out(block, 1, 1, () => {
    lookup.delete(block.key);
  });
}
function update_keyed_each(old_blocks, dirty, get_key, dynamic, ctx, list, lookup, node, destroy, create_each_block6, next, get_context) {
  let o = old_blocks.length;
  let n = list.length;
  let i = o;
  const old_indexes = {};
  while (i--)
    old_indexes[old_blocks[i].key] = i;
  const new_blocks = [];
  const new_lookup = /* @__PURE__ */ new Map();
  const deltas = /* @__PURE__ */ new Map();
  const updates = [];
  i = n;
  while (i--) {
    const child_ctx = get_context(ctx, list, i);
    const key = get_key(child_ctx);
    let block = lookup.get(key);
    if (!block) {
      block = create_each_block6(key, child_ctx);
      block.c();
    } else if (dynamic) {
      updates.push(() => block.p(child_ctx, dirty));
    }
    new_lookup.set(key, new_blocks[i] = block);
    if (key in old_indexes)
      deltas.set(key, Math.abs(i - old_indexes[key]));
  }
  const will_move = /* @__PURE__ */ new Set();
  const did_move = /* @__PURE__ */ new Set();
  function insert2(block) {
    transition_in(block, 1);
    block.m(node, next);
    lookup.set(block.key, block);
    next = block.first;
    n--;
  }
  while (o && n) {
    const new_block = new_blocks[n - 1];
    const old_block = old_blocks[o - 1];
    const new_key = new_block.key;
    const old_key = old_block.key;
    if (new_block === old_block) {
      next = new_block.first;
      o--;
      n--;
    } else if (!new_lookup.has(old_key)) {
      destroy(old_block, lookup);
      o--;
    } else if (!lookup.has(new_key) || will_move.has(new_key)) {
      insert2(new_block);
    } else if (did_move.has(old_key)) {
      o--;
    } else if (deltas.get(new_key) > deltas.get(old_key)) {
      did_move.add(new_key);
      insert2(new_block);
    } else {
      will_move.add(old_key);
      o--;
    }
  }
  while (o--) {
    const old_block = old_blocks[o];
    if (!new_lookup.has(old_block.key))
      destroy(old_block, lookup);
  }
  while (n)
    insert2(new_blocks[n - 1]);
  run_all(updates);
  return new_blocks;
}

// node_modules/svelte/src/shared/boolean_attributes.js
var _boolean_attributes = (
  /** @type {const} */
  [
    "allowfullscreen",
    "allowpaymentrequest",
    "async",
    "autofocus",
    "autoplay",
    "checked",
    "controls",
    "default",
    "defer",
    "disabled",
    "formnovalidate",
    "hidden",
    "inert",
    "ismap",
    "loop",
    "multiple",
    "muted",
    "nomodule",
    "novalidate",
    "open",
    "playsinline",
    "readonly",
    "required",
    "reversed",
    "selected"
  ]
);
var boolean_attributes = /* @__PURE__ */ new Set([..._boolean_attributes]);

// node_modules/svelte/src/runtime/internal/Component.js
function bind(component, name, callback) {
  const index = component.$$.props[name];
  if (index !== void 0) {
    component.$$.bound[index] = callback;
    callback(component.$$.ctx[index]);
  }
}
function create_component(block) {
  block && block.c();
}
function mount_component(component, target, anchor) {
  const { fragment, after_update } = component.$$;
  fragment && fragment.m(target, anchor);
  add_render_callback(() => {
    const new_on_destroy = component.$$.on_mount.map(run).filter(is_function);
    if (component.$$.on_destroy) {
      component.$$.on_destroy.push(...new_on_destroy);
    } else {
      run_all(new_on_destroy);
    }
    component.$$.on_mount = [];
  });
  after_update.forEach(add_render_callback);
}
function destroy_component(component, detaching) {
  const $$ = component.$$;
  if ($$.fragment !== null) {
    flush_render_callbacks($$.after_update);
    run_all($$.on_destroy);
    $$.fragment && $$.fragment.d(detaching);
    $$.on_destroy = $$.fragment = null;
    $$.ctx = [];
  }
}
function make_dirty(component, i) {
  if (component.$$.dirty[0] === -1) {
    dirty_components.push(component);
    schedule_update();
    component.$$.dirty.fill(0);
  }
  component.$$.dirty[i / 31 | 0] |= 1 << i % 31;
}
function init(component, options, instance6, create_fragment6, not_equal, props, append_styles2 = null, dirty = [-1]) {
  const parent_component = current_component;
  set_current_component(component);
  const $$ = component.$$ = {
    fragment: null,
    ctx: [],
    // state
    props,
    update: noop,
    not_equal,
    bound: blank_object(),
    // lifecycle
    on_mount: [],
    on_destroy: [],
    on_disconnect: [],
    before_update: [],
    after_update: [],
    context: new Map(options.context || (parent_component ? parent_component.$$.context : [])),
    // everything else
    callbacks: blank_object(),
    dirty,
    skip_bound: false,
    root: options.target || parent_component.$$.root
  };
  append_styles2 && append_styles2($$.root);
  let ready = false;
  $$.ctx = instance6 ? instance6(component, options.props || {}, (i, ret, ...rest) => {
    const value = rest.length ? rest[0] : ret;
    if ($$.ctx && not_equal($$.ctx[i], $$.ctx[i] = value)) {
      if (!$$.skip_bound && $$.bound[i])
        $$.bound[i](value);
      if (ready)
        make_dirty(component, i);
    }
    return ret;
  }) : [];
  $$.update();
  ready = true;
  run_all($$.before_update);
  $$.fragment = create_fragment6 ? create_fragment6($$.ctx) : false;
  if (options.target) {
    if (options.hydrate) {
      start_hydrating();
      const nodes = children(options.target);
      $$.fragment && $$.fragment.l(nodes);
      nodes.forEach(detach);
    } else {
      $$.fragment && $$.fragment.c();
    }
    if (options.intro)
      transition_in(component.$$.fragment);
    mount_component(component, options.target, options.anchor);
    end_hydrating();
    flush();
  }
  set_current_component(parent_component);
}
var SvelteElement;
if (typeof HTMLElement === "function") {
  SvelteElement = class extends HTMLElement {
    constructor($$componentCtor, $$slots, use_shadow_dom) {
      super();
      /** The Svelte component constructor */
      __publicField(this, "$$ctor");
      /** Slots */
      __publicField(this, "$$s");
      /** The Svelte component instance */
      __publicField(this, "$$c");
      /** Whether or not the custom element is connected */
      __publicField(this, "$$cn", false);
      /** Component props data */
      __publicField(this, "$$d", {});
      /** `true` if currently in the process of reflecting component props back to attributes */
      __publicField(this, "$$r", false);
      /** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */
      __publicField(this, "$$p_d", {});
      /** @type {Record<string, Function[]>} Event listeners */
      __publicField(this, "$$l", {});
      /** @type {Map<Function, Function>} Event listener unsubscribe functions */
      __publicField(this, "$$l_u", /* @__PURE__ */ new Map());
      this.$$ctor = $$componentCtor;
      this.$$s = $$slots;
      if (use_shadow_dom) {
        this.attachShadow({ mode: "open" });
      }
    }
    addEventListener(type, listener, options) {
      this.$$l[type] = this.$$l[type] || [];
      this.$$l[type].push(listener);
      if (this.$$c) {
        const unsub = this.$$c.$on(type, listener);
        this.$$l_u.set(listener, unsub);
      }
      super.addEventListener(type, listener, options);
    }
    removeEventListener(type, listener, options) {
      super.removeEventListener(type, listener, options);
      if (this.$$c) {
        const unsub = this.$$l_u.get(listener);
        if (unsub) {
          unsub();
          this.$$l_u.delete(listener);
        }
      }
    }
    async connectedCallback() {
      this.$$cn = true;
      if (!this.$$c) {
        let create_slot = function(name) {
          return () => {
            let node;
            const obj = {
              c: function create() {
                node = element("slot");
                if (name !== "default") {
                  attr(node, "name", name);
                }
              },
              /**
               * @param {HTMLElement} target
               * @param {HTMLElement} [anchor]
               */
              m: function mount(target, anchor) {
                insert(target, node, anchor);
              },
              d: function destroy(detaching) {
                if (detaching) {
                  detach(node);
                }
              }
            };
            return obj;
          };
        };
        await Promise.resolve();
        if (!this.$$cn) {
          return;
        }
        const $$slots = {};
        const existing_slots = get_custom_elements_slots(this);
        for (const name of this.$$s) {
          if (name in existing_slots) {
            $$slots[name] = [create_slot(name)];
          }
        }
        for (const attribute of this.attributes) {
          const name = this.$$g_p(attribute.name);
          if (!(name in this.$$d)) {
            this.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, "toProp");
          }
        }
        this.$$c = new this.$$ctor({
          target: this.shadowRoot || this,
          props: {
            ...this.$$d,
            $$slots,
            $$scope: {
              ctx: []
            }
          }
        });
        const reflect_attributes = () => {
          this.$$r = true;
          for (const key in this.$$p_d) {
            this.$$d[key] = this.$$c.$$.ctx[this.$$c.$$.props[key]];
            if (this.$$p_d[key].reflect) {
              const attribute_value = get_custom_element_value(
                key,
                this.$$d[key],
                this.$$p_d,
                "toAttribute"
              );
              if (attribute_value == null) {
                this.removeAttribute(this.$$p_d[key].attribute || key);
              } else {
                this.setAttribute(this.$$p_d[key].attribute || key, attribute_value);
              }
            }
          }
          this.$$r = false;
        };
        this.$$c.$$.after_update.push(reflect_attributes);
        reflect_attributes();
        for (const type in this.$$l) {
          for (const listener of this.$$l[type]) {
            const unsub = this.$$c.$on(type, listener);
            this.$$l_u.set(listener, unsub);
          }
        }
        this.$$l = {};
      }
    }
    // We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte
    // and setting attributes through setAttribute etc, this is helpful
    attributeChangedCallback(attr2, _oldValue, newValue) {
      var _a;
      if (this.$$r)
        return;
      attr2 = this.$$g_p(attr2);
      this.$$d[attr2] = get_custom_element_value(attr2, newValue, this.$$p_d, "toProp");
      (_a = this.$$c) == null ? void 0 : _a.$set({ [attr2]: this.$$d[attr2] });
    }
    disconnectedCallback() {
      this.$$cn = false;
      Promise.resolve().then(() => {
        if (!this.$$cn) {
          this.$$c.$destroy();
          this.$$c = void 0;
        }
      });
    }
    $$g_p(attribute_name) {
      return Object.keys(this.$$p_d).find(
        (key) => this.$$p_d[key].attribute === attribute_name || !this.$$p_d[key].attribute && key.toLowerCase() === attribute_name
      ) || attribute_name;
    }
  };
}
function get_custom_element_value(prop, value, props_definition, transform) {
  var _a;
  const type = (_a = props_definition[prop]) == null ? void 0 : _a.type;
  value = type === "Boolean" && typeof value !== "boolean" ? value != null : value;
  if (!transform || !props_definition[prop]) {
    return value;
  } else if (transform === "toAttribute") {
    switch (type) {
      case "Object":
      case "Array":
        return value == null ? null : JSON.stringify(value);
      case "Boolean":
        return value ? "" : null;
      case "Number":
        return value == null ? null : value;
      default:
        return value;
    }
  } else {
    switch (type) {
      case "Object":
      case "Array":
        return value && JSON.parse(value);
      case "Boolean":
        return value;
      case "Number":
        return value != null ? +value : value;
      default:
        return value;
    }
  }
}
var SvelteComponent = class {
  constructor() {
    /**
     * ### PRIVATE API
     *
     * Do not use, may change at any time
     *
     * @type {any}
     */
    __publicField(this, "$$");
    /**
     * ### PRIVATE API
     *
     * Do not use, may change at any time
     *
     * @type {any}
     */
    __publicField(this, "$$set");
  }
  /** @returns {void} */
  $destroy() {
    destroy_component(this, 1);
    this.$destroy = noop;
  }
  /**
   * @template {Extract<keyof Events, string>} K
   * @param {K} type
   * @param {((e: Events[K]) => void) | null | undefined} callback
   * @returns {() => void}
   */
  $on(type, callback) {
    if (!is_function(callback)) {
      return noop;
    }
    const callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);
    callbacks.push(callback);
    return () => {
      const index = callbacks.indexOf(callback);
      if (index !== -1)
        callbacks.splice(index, 1);
    };
  }
  /**
   * @param {Partial<Props>} props
   * @returns {void}
   */
  $set(props) {
    if (this.$$set && !is_empty(props)) {
      this.$$.skip_bound = true;
      this.$$set(props);
      this.$$.skip_bound = false;
    }
  }
};

// node_modules/svelte/src/shared/version.js
var PUBLIC_VERSION = "4";

// node_modules/svelte/src/runtime/internal/disclose-version/index.js
if (typeof window !== "undefined")
  (window.__svelte || (window.__svelte = { v: /* @__PURE__ */ new Set() })).v.add(PUBLIC_VERSION);

// node_modules/tslib/tslib.es6.js
function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}

// src/AddPropInput.svelte
function add_css(target) {
  append_styles(target, "svelte-gwxj8w", "input.svelte-gwxj8w{max-height:25px;overflow-x:auto}select.svelte-gwxj8w{height:21px}#name-input.svelte-gwxj8w{flex-grow:0;width:auto}.modal-input-container.svelte-gwxj8w{width:95%;display:flex;flex-direction:row;align-items:center;gap:10px;margin-top:10px}.btn-inactive.svelte-gwxj8w{cursor:not-allowed;pointer-events:none;opacity:0.7}");
}
function get_each_context(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[19] = list[i];
  return child_ctx;
}
function create_each_block(ctx) {
  let option;
  let t_value = (
    /*key*/
    ctx[19] + ""
  );
  let t;
  let option_value_value;
  return {
    c() {
      option = element("option");
      t = text(t_value);
      option.__value = option_value_value = /*options*/
      ctx[8][
        /*key*/
        ctx[19]
      ];
      set_input_value(option, option.__value);
    },
    m(target, anchor) {
      insert(target, option, anchor);
      append(option, t);
    },
    p: noop,
    d(detaching) {
      if (detaching) {
        detach(option);
      }
    }
  };
}
function create_fragment(ctx) {
  let div;
  let a;
  let t0;
  let a_class_value;
  let a_tabindex_value;
  let t1;
  let select;
  let t2;
  let input0;
  let t3;
  let input1;
  let mounted;
  let dispose;
  let each_value = ensure_array_like(Object.keys(
    /*options*/
    ctx[8]
  ));
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block(get_each_context(ctx, each_value, i));
  }
  return {
    c() {
      div = element("div");
      a = element("a");
      t0 = text("X");
      t1 = space();
      select = element("select");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      t2 = space();
      input0 = element("input");
      t3 = space();
      input1 = element("input");
      attr(a, "id", "del-btn");
      attr(a, "class", a_class_value = "btn-del " + /*isFirst*/
      (ctx[2] ? "btn-inactive" : "") + " svelte-gwxj8w");
      attr(a, "tabindex", a_tabindex_value = /*isFirst*/
      ctx[2] ? -1 : 0);
      attr(a, "href", "href");
      attr(select, "id", "type-input");
      attr(select, "class", "flex-obj svelte-gwxj8w");
      if (
        /*optionVal*/
        ctx[7] === void 0
      )
        add_render_callback(() => (
          /*select_change_handler*/
          ctx[12].call(select)
        ));
      attr(input0, "id", "name-input");
      attr(input0, "class", "name-input flex-obj svelte-gwxj8w");
      attr(input0, "type", "text");
      attr(input0, "name", "name[]");
      attr(input0, "placeholder", "name");
      input0.required = true;
      attr(input1, "id", "value-input");
      attr(input1, "type", "text");
      attr(input1, "name", "value[]");
      attr(input1, "placeholder", "value");
      attr(input1, "class", "value-input flex-obj svelte-gwxj8w");
      attr(div, "class", "modal-input-container svelte-gwxj8w");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      append(div, a);
      append(a, t0);
      append(div, t1);
      append(div, select);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(select, null);
        }
      }
      select_option(
        select,
        /*optionVal*/
        ctx[7],
        true
      );
      append(div, t2);
      append(div, input0);
      ctx[14](input0);
      set_input_value(
        input0,
        /*nameVal*/
        ctx[0]
      );
      append(div, t3);
      append(div, input1);
      ctx[16](input1);
      set_input_value(
        input1,
        /*valueVal*/
        ctx[1]
      );
      if (!mounted) {
        dispose = [
          listen(
            a,
            "click",
            /*click_handler*/
            ctx[11]
          ),
          listen(
            select,
            "change",
            /*select_change_handler*/
            ctx[12]
          ),
          listen(
            select,
            "change",
            /*change_handler*/
            ctx[13]
          ),
          listen(
            input0,
            "input",
            /*input0_input_handler*/
            ctx[15]
          ),
          listen(
            input1,
            "input",
            /*input1_input_handler*/
            ctx[17]
          )
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if (dirty & /*isFirst*/
      4 && a_class_value !== (a_class_value = "btn-del " + /*isFirst*/
      (ctx2[2] ? "btn-inactive" : "") + " svelte-gwxj8w")) {
        attr(a, "class", a_class_value);
      }
      if (dirty & /*isFirst*/
      4 && a_tabindex_value !== (a_tabindex_value = /*isFirst*/
      ctx2[2] ? -1 : 0)) {
        attr(a, "tabindex", a_tabindex_value);
      }
      if (dirty & /*options, Object*/
      256) {
        each_value = ensure_array_like(Object.keys(
          /*options*/
          ctx2[8]
        ));
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(select, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*optionVal, options, Object*/
      384) {
        select_option(
          select,
          /*optionVal*/
          ctx2[7]
        );
      }
      if (dirty & /*nameVal*/
      1 && input0.value !== /*nameVal*/
      ctx2[0]) {
        set_input_value(
          input0,
          /*nameVal*/
          ctx2[0]
        );
      }
      if (dirty & /*valueVal*/
      2 && input1.value !== /*valueVal*/
      ctx2[1]) {
        set_input_value(
          input1,
          /*valueVal*/
          ctx2[1]
        );
      }
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_each(each_blocks, detaching);
      ctx[14](null);
      ctx[16](null);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance($$self, $$props, $$invalidate) {
  let { isFirst } = $$props;
  let { removeInput } = $$props;
  let { id } = $$props;
  let { typeVal = "text" } = $$props;
  let { nameVal = "" } = $$props;
  let { valueVal = "" } = $$props;
  let inputEl;
  let valueEl;
  let optionVal;
  const options = {
    Text: "string",
    Number: "number",
    Checkbox: "checkbox",
    Date: "date",
    Datetime: "datetime-local"
  };
  const convertProps = {
    text: "Text",
    multitext: "Text",
    number: "Number",
    checkbox: "Checkbox",
    date: "Date",
    datetime: "Datetime"
  };
  onMount(() => __awaiter(void 0, void 0, void 0, function* () {
    yield tick();
    inputEl.focus();
    inputEl.select();
    $$invalidate(7, optionVal = options[convertProps[typeVal]]);
  }));
  function changeType(type) {
    $$invalidate(6, valueEl.type = type, valueEl);
  }
  const click_handler = () => isFirst ? false : removeInput(id);
  function select_change_handler() {
    optionVal = select_value(this);
    $$invalidate(7, optionVal);
    $$invalidate(8, options);
  }
  const change_handler = () => changeType(optionVal);
  function input0_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      inputEl = $$value;
      $$invalidate(5, inputEl);
    });
  }
  function input0_input_handler() {
    nameVal = this.value;
    $$invalidate(0, nameVal);
  }
  function input1_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      valueEl = $$value;
      $$invalidate(6, valueEl);
    });
  }
  function input1_input_handler() {
    valueVal = this.value;
    $$invalidate(1, valueVal);
  }
  $$self.$$set = ($$props2) => {
    if ("isFirst" in $$props2)
      $$invalidate(2, isFirst = $$props2.isFirst);
    if ("removeInput" in $$props2)
      $$invalidate(3, removeInput = $$props2.removeInput);
    if ("id" in $$props2)
      $$invalidate(4, id = $$props2.id);
    if ("typeVal" in $$props2)
      $$invalidate(10, typeVal = $$props2.typeVal);
    if ("nameVal" in $$props2)
      $$invalidate(0, nameVal = $$props2.nameVal);
    if ("valueVal" in $$props2)
      $$invalidate(1, valueVal = $$props2.valueVal);
  };
  return [
    nameVal,
    valueVal,
    isFirst,
    removeInput,
    id,
    inputEl,
    valueEl,
    optionVal,
    options,
    changeType,
    typeVal,
    click_handler,
    select_change_handler,
    change_handler,
    input0_binding,
    input0_input_handler,
    input1_binding,
    input1_input_handler
  ];
}
var AddPropInput = class extends SvelteComponent {
  constructor(options) {
    super();
    init(
      this,
      options,
      instance,
      create_fragment,
      safe_not_equal,
      {
        isFirst: 2,
        removeInput: 3,
        id: 4,
        typeVal: 10,
        nameVal: 0,
        valueVal: 1
      },
      add_css
    );
  }
};
var AddPropInput_default = AddPropInput;

// src/helpers.ts
function parseValue(input, type) {
  switch (type) {
    case "number":
      return Number(input.value);
    case "checkbox":
      return Boolean(input.checked);
    default:
      return input.value;
  }
}
function removeExtraCommas(str) {
  return str.replace(/^,*(.*?),*$/g, "$1").replace(/,{2,}/g, ",");
}
function cleanTags(str) {
  let cleanStr = str;
  for (let index in KNOWN_BAD_CHARACTERS) {
    cleanStr = cleanStr.replaceAll(KNOWN_BAD_CHARACTERS[index], "");
  }
  return cleanStr;
}
var KNOWN_BAD_CHARACTERS = [
  "\u2012",
  "\u2013",
  "\u2014",
  "\u2015",
  "\u204F",
  "\u203D",
  "\u2018",
  "\u201A",
  "\u201B",
  "\u2039",
  "\u203A",
  "\u201C",
  "\u201D",
  "\u201E",
  "\u201F",
  "\u2045",
  "\u2046",
  "\u204B",
  "\u204E",
  "\u2051",
  "\u2044",
  "\u204A",
  "\u2030",
  "\u2031",
  "\u2052",
  "\u2020",
  "\u2021",
  "\u2022",
  "\u2023",
  "\u2043",
  "\u204C",
  "\u204D",
  "\u2032",
  "\u2035",
  "\u2038",
  "\u203B",
  "\u2050",
  "\u2041",
  "\u2042",
  "\u2016",
  "\u2011",
  "\u2033",
  "\u2034",
  "\u2057",
  "\u2036",
  "\u2037",
  "`",
  "^",
  "\u203E",
  "\u2017",
  "\u2053",
  ";",
  ":",
  "!",
  "\u203C",
  "\u2049",
  "?",
  "\u2048",
  "\u2047",
  ".",
  "\u2024",
  "\u2025",
  "\u2026",
  "'",
  '"',
  "(",
  ")",
  "[",
  "]",
  "{",
  "}",
  "@",
  "*",
  "&",
  "%",
  "\u2054",
  "+",
  "<",
  "=",
  ">",
  "|",
  "~",
  "$",
  "\u2055",
  "\u2056",
  "\u2058",
  "\u2059",
  "\u205A",
  "\u205B",
  "\u205C",
  "\u205D",
  "\u205E",
  "\u2E00",
  "\u2E01",
  "\u2E02",
  "\u2E03",
  "\u2E04",
  "\u2E05",
  "\u2E06",
  "\u2E07",
  "\u2E08",
  "\u2E09",
  "\u2E0A",
  "\u2E0B",
  "\u2E0C",
  "\u2E0D",
  "\u2E0E",
  "\u2E0F",
  "\u2E10",
  "\u2E11",
  "\u2E12",
  "\u2E13",
  "\u2E14",
  "\u2E15",
  "\u2E16",
  "\u2E17",
  "\u2E1C",
  "\u2E1D",
  " ",
  "#"
];

// src/AddPropForm.svelte
var { Map: Map_1 } = globals;
function add_css2(target) {
  append_styles(target, "svelte-1ied4k", ".modal-inputs-container.svelte-1ied4k{height:200px;width:100%;overflow-y:scroll;border-radius:5px;border-style:solid;display:flex;flex-direction:column;align-items:center}.modal-add-container.svelte-1ied4k{margin-top:10px}.alert-container.svelte-1ied4k{display:flex;flex-direction:column;align-items:center;justify-content:center;margin-bottom:10px;background-color:red;font-weight:bold}.hidden.svelte-1ied4k{display:none}");
}
function get_each_context2(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[22] = list[i];
  child_ctx[23] = list;
  child_ctx[24] = i;
  return child_ctx;
}
function create_each_block2(key_1, ctx) {
  let first;
  let propinput;
  let updating_typeVal;
  let updating_nameVal;
  let current;
  function propinput_typeVal_binding(value) {
    ctx[15](
      value,
      /*input*/
      ctx[22]
    );
  }
  function propinput_nameVal_binding(value) {
    ctx[16](
      value,
      /*input*/
      ctx[22]
    );
  }
  let propinput_props = {
    isFirst: (
      /*input*/
      ctx[22].isFirst
    ),
    id: (
      /*input*/
      ctx[22].id
    ),
    valueVal: (
      /*input*/
      ctx[22].valueDef
    ),
    removeInput: (
      /*removeInput*/
      ctx[8]
    )
  };
  if (
    /*input*/
    ctx[22].typeDef !== void 0
  ) {
    propinput_props.typeVal = /*input*/
    ctx[22].typeDef;
  }
  if (
    /*input*/
    ctx[22].nameDef !== void 0
  ) {
    propinput_props.nameVal = /*input*/
    ctx[22].nameDef;
  }
  propinput = new AddPropInput_default({ props: propinput_props });
  binding_callbacks.push(() => bind(propinput, "typeVal", propinput_typeVal_binding));
  binding_callbacks.push(() => bind(propinput, "nameVal", propinput_nameVal_binding));
  return {
    key: key_1,
    first: null,
    c() {
      first = empty();
      create_component(propinput.$$.fragment);
      this.first = first;
    },
    m(target, anchor) {
      insert(target, first, anchor);
      mount_component(propinput, target, anchor);
      current = true;
    },
    p(new_ctx, dirty) {
      ctx = new_ctx;
      const propinput_changes = {};
      if (dirty & /*inputEls*/
      32)
        propinput_changes.isFirst = /*input*/
        ctx[22].isFirst;
      if (dirty & /*inputEls*/
      32)
        propinput_changes.id = /*input*/
        ctx[22].id;
      if (dirty & /*inputEls*/
      32)
        propinput_changes.valueVal = /*input*/
        ctx[22].valueDef;
      if (!updating_typeVal && dirty & /*inputEls*/
      32) {
        updating_typeVal = true;
        propinput_changes.typeVal = /*input*/
        ctx[22].typeDef;
        add_flush_callback(() => updating_typeVal = false);
      }
      if (!updating_nameVal && dirty & /*inputEls*/
      32) {
        updating_nameVal = true;
        propinput_changes.nameVal = /*input*/
        ctx[22].nameDef;
        add_flush_callback(() => updating_nameVal = false);
      }
      propinput.$set(propinput_changes);
    },
    i(local) {
      if (current)
        return;
      transition_in(propinput.$$.fragment, local);
      current = true;
    },
    o(local) {
      transition_out(propinput.$$.fragment, local);
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(first);
      }
      destroy_component(propinput, detaching);
    }
  };
}
function create_fragment2(ctx) {
  let div6;
  let div2;
  let div0;
  let t1;
  let div1;
  let t2;
  let t3;
  let p0;
  let t5;
  let p1;
  let t6;
  let t7;
  let t8;
  let t9;
  let p2;
  let t11;
  let form;
  let label;
  let input_1;
  let t12_value = "Overwrite existing properties";
  let t12;
  let t13;
  let div3;
  let each_blocks = [];
  let each_1_lookup = new Map_1();
  let t14;
  let div4;
  let a;
  let t16;
  let div5;
  let button;
  let current;
  let mounted;
  let dispose;
  let each_value = ensure_array_like(
    /*inputEls*/
    ctx[5]
  );
  const get_key = (ctx2) => (
    /*input*/
    ctx2[22].id
  );
  for (let i = 0; i < each_value.length; i += 1) {
    let child_ctx = get_each_context2(ctx, each_value, i);
    let key = get_key(child_ctx);
    each_1_lookup.set(key, each_blocks[i] = create_each_block2(key, child_ctx));
  }
  return {
    c() {
      div6 = element("div");
      div2 = element("div");
      div0 = element("div");
      div0.textContent = "ERROR";
      t1 = space();
      div1 = element("div");
      t2 = text(
        /*alertText*/
        ctx[4]
      );
      t3 = space();
      p0 = element("p");
      p0.textContent = "Type in a property name, then value. Use the dropbox to choose what type of\r\n    data you wish to store.";
      t5 = space();
      p1 = element("p");
      t6 = text('If you want to make a List property, use the Text data type and separate\r\n    each value with a "');
      t7 = text(
        /*delimiter*/
        ctx[1]
      );
      t8 = text('".');
      t9 = space();
      p2 = element("p");
      p2.textContent = 'If you want to add Tags, use the name "tags".';
      t11 = space();
      form = element("form");
      label = element("label");
      input_1 = element("input");
      t12 = text(t12_value);
      t13 = space();
      div3 = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      t14 = space();
      div4 = element("div");
      a = element("a");
      a.textContent = "Add";
      t16 = space();
      div5 = element("div");
      button = element("button");
      button.textContent = "Submit";
      attr(div1, "id", "alert-text");
      attr(div2, "id", "alert-container");
      attr(div2, "class", "alert-container hidden svelte-1ied4k");
      attr(input_1, "type", "checkbox");
      input_1.checked = /*overwrite*/
      ctx[0];
      attr(div3, "class", "modal-inputs-container svelte-1ied4k");
      attr(a, "class", "a-btn");
      attr(a, "href", "href");
      attr(div4, "class", "modal-add-container svelte-1ied4k");
      attr(button, "class", "btn-submit");
      attr(div5, "class", "modal-button-container");
      attr(div6, "id", "multi-properties-modal");
      attr(div6, "class", "modal-content");
    },
    m(target, anchor) {
      insert(target, div6, anchor);
      append(div6, div2);
      append(div2, div0);
      append(div2, t1);
      append(div2, div1);
      append(div1, t2);
      ctx[14](div2);
      append(div6, t3);
      append(div6, p0);
      append(div6, t5);
      append(div6, p1);
      append(p1, t6);
      append(p1, t7);
      append(p1, t8);
      append(div6, t9);
      append(div6, p2);
      append(div6, t11);
      append(div6, form);
      append(form, label);
      append(label, input_1);
      append(label, t12);
      append(form, t13);
      append(form, div3);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div3, null);
        }
      }
      append(form, t14);
      append(form, div4);
      append(div4, a);
      append(form, t16);
      append(form, div5);
      append(div5, button);
      ctx[18](form);
      current = true;
      if (!mounted) {
        dispose = [
          listen(
            input_1,
            "change",
            /*onCheckboxChange*/
            ctx[6]
          ),
          listen(
            a,
            "click",
            /*click_handler*/
            ctx[17]
          ),
          listen(
            button,
            "click",
            /*onSubmit*/
            ctx[9]
          ),
          listen(form, "submit", prevent_default(
            /*submit_handler*/
            ctx[13]
          ))
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if (!current || dirty & /*alertText*/
      16)
        set_data(
          t2,
          /*alertText*/
          ctx2[4]
        );
      if (!current || dirty & /*delimiter*/
      2)
        set_data(
          t7,
          /*delimiter*/
          ctx2[1]
        );
      if (!current || dirty & /*overwrite*/
      1) {
        input_1.checked = /*overwrite*/
        ctx2[0];
      }
      if (dirty & /*inputEls, removeInput*/
      288) {
        each_value = ensure_array_like(
          /*inputEls*/
          ctx2[5]
        );
        group_outros();
        each_blocks = update_keyed_each(each_blocks, dirty, get_key, 1, ctx2, each_value, each_1_lookup, div3, outro_and_destroy_block, create_each_block2, null, get_each_context2);
        check_outros();
      }
    },
    i(local) {
      if (current)
        return;
      for (let i = 0; i < each_value.length; i += 1) {
        transition_in(each_blocks[i]);
      }
      current = true;
    },
    o(local) {
      for (let i = 0; i < each_blocks.length; i += 1) {
        transition_out(each_blocks[i]);
      }
      current = false;
    },
    d(detaching) {
      if (detaching) {
        detach(div6);
      }
      ctx[14](null);
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].d();
      }
      ctx[18](null);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance2($$self, $$props, $$invalidate) {
  let { submission } = $$props;
  let { overwrite } = $$props;
  let { delimiter = "," } = $$props;
  let { defaultProps = [] } = $$props;
  let { changeBool } = $$props;
  let countInputs = 0;
  let formEl;
  let errorEl;
  let alertText = ".";
  let inputEls = [];
  function onCheckboxChange() {
    $$invalidate(0, overwrite = !overwrite);
    changeBool(overwrite);
  }
  onMount(() => {
    defaultProps.length > 0 ? addInputs(defaultProps) : addInputs([{ type: "text", name: "", value: "" }]);
  });
  function addInputs(inputs = [{ type: "text", name: "", value: "" }]) {
    let arr = [];
    for (let input of inputs) {
      countInputs++;
      arr.push({
        id: countInputs,
        isFirst: countInputs === 1 ? true : false,
        typeDef: input.type,
        nameDef: input.name,
        valueDef: input.value
      });
    }
    $$invalidate(5, inputEls = [...inputEls, ...arr]);
  }
  function removeInput(id) {
    return __awaiter(this, void 0, void 0, function* () {
      $$invalidate(5, inputEls = inputEls.filter((input) => input.id !== id));
      yield tick();
      let inputs = formEl.querySelectorAll("input");
      if (!inputs)
        return;
      inputs[inputs.length - 2].focus();
    });
  }
  function checkDuplicateNames() {
    let set = /* @__PURE__ */ new Set();
    for (let input of inputEls)
      set.add(input.nameDef);
    if (set.size < inputEls.length)
      return true;
    else
      return false;
  }
  function runError(errorText) {
    $$invalidate(4, alertText = errorText);
    errorEl.classList.remove("hidden");
  }
  function onSubmit() {
    if (checkDuplicateNames()) {
      runError("Duplicate property names are not allowed.");
      return;
    }
    let obj = /* @__PURE__ */ new Map();
    let inputs = formEl.querySelectorAll('input[name^="name[]"]');
    inputs.forEach((input) => {
      if (!(input.nextElementSibling instanceof HTMLInputElement))
        return;
      if (!(input.previousElementSibling instanceof HTMLSelectElement))
        return;
      if (!(input.previousElementSibling.children[0] instanceof HTMLOptionElement))
        return;
      let name = input.value;
      if (name === "") {
        input.reportValidity();
        return;
      }
      let value = parseValue(input.nextElementSibling, input.nextElementSibling.type);
      if (typeof value === "string") {
        if (name === "tags") {
          value = cleanTags(value);
        }
        if (value.contains(delimiter)) {
          let str = removeExtraCommas(value);
          value = str.split(delimiter);
        }
      }
      if (value === "")
        value = null;
      let inputType = input.previousElementSibling.children[0].innerText.toLowerCase();
      let propObj = {
        type: inputType,
        data: value,
        overwrite: false,
        delimiter
      };
      obj.set(name, propObj);
    });
    if (obj.size < inputs.length)
      return;
    console.log(obj);
    submission(obj);
  }
  function submit_handler(event) {
    bubble.call(this, $$self, event);
  }
  function div2_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      errorEl = $$value;
      $$invalidate(3, errorEl);
    });
  }
  function propinput_typeVal_binding(value, input) {
    if ($$self.$$.not_equal(input.typeDef, value)) {
      input.typeDef = value;
      $$invalidate(5, inputEls);
    }
  }
  function propinput_nameVal_binding(value, input) {
    if ($$self.$$.not_equal(input.nameDef, value)) {
      input.nameDef = value;
      $$invalidate(5, inputEls);
    }
  }
  const click_handler = () => addInputs([{ type: "text", name: "", value: "" }]);
  function form_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      formEl = $$value;
      $$invalidate(2, formEl);
    });
  }
  $$self.$$set = ($$props2) => {
    if ("submission" in $$props2)
      $$invalidate(10, submission = $$props2.submission);
    if ("overwrite" in $$props2)
      $$invalidate(0, overwrite = $$props2.overwrite);
    if ("delimiter" in $$props2)
      $$invalidate(1, delimiter = $$props2.delimiter);
    if ("defaultProps" in $$props2)
      $$invalidate(11, defaultProps = $$props2.defaultProps);
    if ("changeBool" in $$props2)
      $$invalidate(12, changeBool = $$props2.changeBool);
  };
  return [
    overwrite,
    delimiter,
    formEl,
    errorEl,
    alertText,
    inputEls,
    onCheckboxChange,
    addInputs,
    removeInput,
    onSubmit,
    submission,
    defaultProps,
    changeBool,
    submit_handler,
    div2_binding,
    propinput_typeVal_binding,
    propinput_nameVal_binding,
    click_handler,
    form_binding
  ];
}
var AddPropForm = class extends SvelteComponent {
  constructor(options) {
    super();
    init(
      this,
      options,
      instance2,
      create_fragment2,
      safe_not_equal,
      {
        submission: 10,
        overwrite: 0,
        delimiter: 1,
        defaultProps: 11,
        changeBool: 12
      },
      add_css2
    );
  }
};
var AddPropForm_default = AddPropForm;

// src/AddConfirmModal.ts
var import_obsidian = require("obsidian");

// src/AddConfirmForm.svelte
function add_css3(target) {
  append_styles(target, "svelte-135liin", ".msg.svelte-135liin{font-weight:bold;padding-bottom:10px}");
}
function get_each_context3(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[7] = list[i][0];
  child_ctx[8] = list[i][1];
  return child_ctx;
}
function create_each_block3(ctx) {
  let li;
  let t0_value = (
    /*propName*/
    ctx[7] + ""
  );
  let t0;
  let t1;
  let t2_value = (
    /*prop*/
    ctx[8].data + ""
  );
  let t2;
  let t3;
  return {
    c() {
      li = element("li");
      t0 = text(t0_value);
      t1 = text(": ");
      t2 = text(t2_value);
      t3 = space();
    },
    m(target, anchor) {
      insert(target, li, anchor);
      append(li, t0);
      append(li, t1);
      append(li, t2);
      append(li, t3);
    },
    p(ctx2, dirty) {
      if (dirty & /*props*/
      1 && t0_value !== (t0_value = /*propName*/
      ctx2[7] + ""))
        set_data(t0, t0_value);
      if (dirty & /*props*/
      1 && t2_value !== (t2_value = /*prop*/
      ctx2[8].data + ""))
        set_data(t2, t2_value);
    },
    d(detaching) {
      if (detaching) {
        detach(li);
      }
    }
  };
}
function create_fragment3(ctx) {
  let div;
  let form;
  let p0;
  let t1;
  let p1;
  let t3;
  let ul;
  let t4;
  let p2;
  let t6;
  let button0;
  let t8;
  let button1;
  let mounted;
  let dispose;
  let each_value = ensure_array_like([.../*props*/
  ctx[0]]);
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block3(get_each_context3(ctx, each_value, i));
  }
  return {
    c() {
      div = element("div");
      form = element("form");
      p0 = element("p");
      p0.textContent = `${/*msg*/
      ctx[4]}`;
      t1 = space();
      p1 = element("p");
      p1.textContent = "The following props will be added:";
      t3 = space();
      ul = element("ul");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      t4 = space();
      p2 = element("p");
      p2.textContent = "Are you sure you wish to proceed?";
      t6 = space();
      button0 = element("button");
      button0.textContent = "Confirm";
      t8 = space();
      button1 = element("button");
      button1.textContent = "Cancel";
      attr(p0, "class", "msg svelte-135liin");
      attr(button0, "class", "mod-warning");
      attr(button0, "type", "submit");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      append(div, form);
      append(form, p0);
      append(form, t1);
      append(form, p1);
      append(form, t3);
      append(form, ul);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(ul, null);
        }
      }
      append(form, t4);
      append(form, p2);
      append(form, t6);
      append(form, button0);
      append(form, t8);
      append(form, button1);
      ctx[6](button1);
      if (!mounted) {
        dispose = [
          listen(button1, "click", function() {
            if (is_function(
              /*cancel*/
              ctx[2]
            ))
              ctx[2].apply(this, arguments);
          }),
          listen(form, "submit", prevent_default(function() {
            if (is_function(
              /*submission*/
              ctx[1]
            ))
              ctx[1].apply(this, arguments);
          }))
        ];
        mounted = true;
      }
    },
    p(new_ctx, [dirty]) {
      ctx = new_ctx;
      if (dirty & /*props*/
      1) {
        each_value = ensure_array_like([.../*props*/
        ctx[0]]);
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context3(ctx, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block3(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(ul, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_each(each_blocks, detaching);
      ctx[6](null);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance3($$self, $$props, $$invalidate) {
  let { props } = $$props;
  let { overwrite = true } = $$props;
  let { submission } = $$props;
  let { cancel } = $$props;
  let btnCancel;
  const msg = overwrite ? "Any pre-existing text props will have their values overwritten." : "Any pre-existing text props will have their values be appended to.";
  onMount(() => {
    btnCancel.focus();
  });
  function button1_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      btnCancel = $$value;
      $$invalidate(3, btnCancel);
    });
  }
  $$self.$$set = ($$props2) => {
    if ("props" in $$props2)
      $$invalidate(0, props = $$props2.props);
    if ("overwrite" in $$props2)
      $$invalidate(5, overwrite = $$props2.overwrite);
    if ("submission" in $$props2)
      $$invalidate(1, submission = $$props2.submission);
    if ("cancel" in $$props2)
      $$invalidate(2, cancel = $$props2.cancel);
  };
  return [props, submission, cancel, btnCancel, msg, overwrite, button1_binding];
}
var AddConfirmForm = class extends SvelteComponent {
  constructor(options) {
    super();
    init(
      this,
      options,
      instance3,
      create_fragment3,
      safe_not_equal,
      {
        props: 0,
        overwrite: 5,
        submission: 1,
        cancel: 2
      },
      add_css3
    );
  }
};
var AddConfirmForm_default = AddConfirmForm;

// src/AddConfirmModal.ts
var AddConfirmModal = class extends import_obsidian.Modal {
  constructor(app, props, overwrite, submission) {
    super(app);
    this.props = props;
    this.overwrite = overwrite;
    this.submission = submission;
  }
  onSubmit() {
    this.submission(true);
    this.close();
  }
  onCancel() {
    this.submission(false);
    this.close();
  }
  onOpen() {
    this.titleEl.createEl("h2", { text: "Add Properties" });
    this.component = new AddConfirmForm_default({
      target: this.contentEl,
      props: {
        props: this.props,
        overwrite: this.overwrite,
        submission: this.onSubmit.bind(this),
        cancel: this.onCancel.bind(this)
      }
    });
  }
};

// src/AddPropModal.ts
var PropModal = class extends import_obsidian2.Modal {
  constructor(app, submission, overwrite, delimiter, defaultProps, changeBool) {
    super(app);
    this.submission = submission;
    this.overwrite = overwrite;
    this.delimiter = delimiter;
    this.defaultProps = defaultProps;
    this.changeBool = changeBool;
  }
  //Run form submission if user clicks confirm.
  onConfirm(bool) {
    if (bool) {
      this.submission(this.props);
      this.close();
    }
  }
  updateBool(bool) {
    this.overwrite = bool;
    this.changeBool(bool);
  }
  //Pull up confirmation form when user submits base form.
  onSubmit(props) {
    this.props = props;
    new AddConfirmModal(
      this.app,
      this.props,
      this.overwrite,
      this.onConfirm.bind(this)
    ).open();
  }
  onOpen() {
    this.titleEl.createEl("h2", { text: "Add Properties" });
    this.component = new AddPropForm_default({
      target: this.contentEl,
      props: {
        submission: this.onSubmit.bind(this),
        overwrite: this.overwrite,
        delimiter: this.delimiter,
        defaultProps: this.defaultProps,
        changeBool: this.updateBool.bind(this)
      }
    });
  }
};

// src/SettingTab.ts
var import_obsidian3 = require("obsidian");
var SettingTab = class extends import_obsidian3.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    let { containerEl } = this;
    containerEl.empty();
    new import_obsidian3.Setting(containerEl).setName("Overwrite existing text").setDesc(
      "When adding a property with a name that already exists, the text will overwrite the prop's existing value.  If left disabled, the new value will be appended to the old as a List."
    ).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.overwrite);
      toggle.onChange(async (value) => {
        this.plugin.settings.overwrite = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian3.Setting(containerEl).setName("Recursive Iteration").setDesc(
      "When toggled on, while looping through all files in a folder, you will also loop through any sub-folders."
    ).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.recursive);
      toggle.onChange(async (value) => {
        this.plugin.settings.recursive = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian3.Setting(containerEl).setName("List Delimiter").setDesc(
      "Set delimiter to use when creating a list.  Commas(,) are used by default."
    ).addText((text2) => {
      text2.setValue(this.plugin.settings.delimiter);
      text2.onChange(async (value) => {
        if (value.length > 1) {
          text2.setValue(value[0]);
          new import_obsidian3.Notice("Delimiter must be a single character.");
          return;
        }
        this.plugin.settings.delimiter = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian3.Setting(containerEl).setName("Default Props File").setDesc(
      "Select a file with properties that you want to load into the Multi Properties form by default.  Type in the full path of the desired file.(ex. Templates/PropFile 1)"
    ).addText((text2) => {
      text2.setValue(this.plugin.settings.defaultPropPath);
      text2.onChange(async (value) => {
        this.plugin.settings.defaultPropPath = value;
        await this.plugin.saveSettings();
      });
    });
  }
};

// src/RemoveModal.ts
var import_obsidian5 = require("obsidian");

// src/RemovePropForm.svelte
function add_css4(target) {
  append_styles(target, "svelte-1264666", ".name-container.svelte-1264666{display:flex;flex-direction:column;gap:5px;margin-top:10px;margin-bottom:20px}.alert-container.svelte-1264666{display:flex;flex-direction:column;align-items:center;justify-content:center;margin-bottom:10px;background-color:red;font-weight:bold}.button-container.svelte-1264666{display:flex;flex-direction:row;justify-content:space-between}.hidden.svelte-1264666{display:none}");
}
function get_each_context4(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[14] = list[i];
  child_ctx[15] = list;
  child_ctx[16] = i;
  return child_ctx;
}
function create_each_block4(ctx) {
  let label;
  let input_1;
  let t0;
  let t1_value = (
    /*input*/
    ctx[14].name + ""
  );
  let t1;
  let t2;
  let mounted;
  let dispose;
  function input_1_change_handler() {
    ctx[12].call(
      input_1,
      /*each_value*/
      ctx[15],
      /*input_index*/
      ctx[16]
    );
  }
  return {
    c() {
      label = element("label");
      input_1 = element("input");
      t0 = space();
      t1 = text(t1_value);
      t2 = space();
      attr(input_1, "type", "checkbox");
    },
    m(target, anchor) {
      insert(target, label, anchor);
      append(label, input_1);
      set_input_value(
        input_1,
        /*input*/
        ctx[14].name
      );
      input_1.checked = /*input*/
      ctx[14].isChecked;
      append(label, t0);
      append(label, t1);
      append(label, t2);
      if (!mounted) {
        dispose = [
          listen(input_1, "change", input_1_change_handler),
          listen(
            input_1,
            "change",
            /*change_handler*/
            ctx[13]
          )
        ];
        mounted = true;
      }
    },
    p(new_ctx, dirty) {
      ctx = new_ctx;
      if (dirty & /*inputs*/
      8) {
        set_input_value(
          input_1,
          /*input*/
          ctx[14].name
        );
      }
      if (dirty & /*inputs*/
      8) {
        input_1.checked = /*input*/
        ctx[14].isChecked;
      }
      if (dirty & /*inputs*/
      8 && t1_value !== (t1_value = /*input*/
      ctx[14].name + ""))
        set_data(t1, t1_value);
    },
    d(detaching) {
      if (detaching) {
        detach(label);
      }
      mounted = false;
      run_all(dispose);
    }
  };
}
function create_fragment4(ctx) {
  let div5;
  let div2;
  let div0;
  let t1;
  let div1;
  let t2;
  let t3;
  let p;
  let t5;
  let form;
  let div3;
  let t6;
  let div4;
  let button0;
  let t8;
  let button1;
  let t9_value = (
    /*isMaxChecked*/
    ctx[0] ? "Uncheck All" : "Check All"
  );
  let t9;
  let mounted;
  let dispose;
  let each_value = ensure_array_like(
    /*inputs*/
    ctx[3]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block4(get_each_context4(ctx, each_value, i));
  }
  return {
    c() {
      div5 = element("div");
      div2 = element("div");
      div0 = element("div");
      div0.textContent = "ERROR";
      t1 = space();
      div1 = element("div");
      t2 = text(
        /*alertText*/
        ctx[2]
      );
      t3 = space();
      p = element("p");
      p.textContent = "Select the properties you wish to remove from the file selection.";
      t5 = space();
      form = element("form");
      div3 = element("div");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      t6 = space();
      div4 = element("div");
      button0 = element("button");
      button0.textContent = "Confirm";
      t8 = space();
      button1 = element("button");
      t9 = text(t9_value);
      attr(div1, "id", "alert-text");
      attr(div2, "id", "alert-container");
      attr(div2, "class", "alert-container hidden svelte-1264666");
      attr(div3, "class", "name-container svelte-1264666");
      attr(button0, "type", "submit");
      attr(div4, "class", "button-container svelte-1264666");
    },
    m(target, anchor) {
      insert(target, div5, anchor);
      append(div5, div2);
      append(div2, div0);
      append(div2, t1);
      append(div2, div1);
      append(div1, t2);
      ctx[11](div2);
      append(div5, t3);
      append(div5, p);
      append(div5, t5);
      append(div5, form);
      append(form, div3);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(div3, null);
        }
      }
      append(form, t6);
      append(form, div4);
      append(div4, button0);
      append(div4, t8);
      append(div4, button1);
      append(button1, t9);
      if (!mounted) {
        dispose = [
          listen(
            button0,
            "click",
            /*onSubmit*/
            ctx[6]
          ),
          listen(
            button1,
            "click",
            /*toggleAll*/
            ctx[5]
          ),
          listen(form, "submit", prevent_default(
            /*submit_handler*/
            ctx[10]
          ))
        ];
        mounted = true;
      }
    },
    p(ctx2, [dirty]) {
      if (dirty & /*alertText*/
      4)
        set_data(
          t2,
          /*alertText*/
          ctx2[2]
        );
      if (dirty & /*inputs, onCheckboxChange*/
      24) {
        each_value = ensure_array_like(
          /*inputs*/
          ctx2[3]
        );
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context4(ctx2, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block4(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(div3, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
      if (dirty & /*isMaxChecked*/
      1 && t9_value !== (t9_value = /*isMaxChecked*/
      ctx2[0] ? "Uncheck All" : "Check All"))
        set_data(t9, t9_value);
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div5);
      }
      ctx[11](null);
      destroy_each(each_blocks, detaching);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance4($$self, $$props, $$invalidate) {
  let isMaxChecked;
  let { names = [] } = $$props;
  let { submission } = $$props;
  let errorEl;
  let alertText = ".";
  let checkCount = 0;
  let inputs = [];
  for (let name of names) {
    inputs.push({ name, isChecked: false });
  }
  names.sort();
  function onCheckboxChange(event) {
    let checked = event.target.checked;
    checked ? $$invalidate(9, checkCount++, checkCount) : $$invalidate(9, checkCount--, checkCount);
  }
  function toggleAll() {
    if (isMaxChecked) {
      $$invalidate(3, inputs = inputs.map((input) => Object.assign(Object.assign({}, input), { isChecked: false })));
      $$invalidate(9, checkCount = 0);
    } else {
      $$invalidate(3, inputs = inputs.map((input) => Object.assign(Object.assign({}, input), { isChecked: true })));
      $$invalidate(9, checkCount = names.length);
    }
  }
  function onSubmit() {
    if (checkCount === 0) {
      $$invalidate(2, alertText = "Please select at least one property to remove.");
      errorEl.classList.remove("hidden");
      return;
    }
    let propNames = inputs.filter((input) => input.isChecked).map((input) => input.name);
    submission(propNames);
  }
  function submit_handler(event) {
    bubble.call(this, $$self, event);
  }
  function div2_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      errorEl = $$value;
      $$invalidate(1, errorEl);
    });
  }
  function input_1_change_handler(each_value, input_index) {
    each_value[input_index].name = this.value;
    each_value[input_index].isChecked = this.checked;
    $$invalidate(3, inputs);
  }
  const change_handler = (event) => onCheckboxChange(event);
  $$self.$$set = ($$props2) => {
    if ("names" in $$props2)
      $$invalidate(7, names = $$props2.names);
    if ("submission" in $$props2)
      $$invalidate(8, submission = $$props2.submission);
  };
  $$self.$$.update = () => {
    if ($$self.$$.dirty & /*checkCount, names*/
    640) {
      $:
        $$invalidate(0, isMaxChecked = checkCount >= names.length);
    }
    if ($$self.$$.dirty & /*isMaxChecked*/
    1) {
      $:
        console.log(isMaxChecked);
    }
  };
  return [
    isMaxChecked,
    errorEl,
    alertText,
    inputs,
    onCheckboxChange,
    toggleAll,
    onSubmit,
    names,
    submission,
    checkCount,
    submit_handler,
    div2_binding,
    input_1_change_handler,
    change_handler
  ];
}
var RemovePropForm = class extends SvelteComponent {
  constructor(options) {
    super();
    init(this, options, instance4, create_fragment4, safe_not_equal, { names: 7, submission: 8 }, add_css4);
  }
};
var RemovePropForm_default = RemovePropForm;

// src/RemoveConfirmModal.ts
var import_obsidian4 = require("obsidian");

// src/RemoveConfirmForm.svelte
function get_each_context5(ctx, list, i) {
  const child_ctx = ctx.slice();
  child_ctx[6] = list[i];
  return child_ctx;
}
function create_each_block5(ctx) {
  let li;
  let t0_value = (
    /*name*/
    ctx[6] + ""
  );
  let t0;
  let t1;
  return {
    c() {
      li = element("li");
      t0 = text(t0_value);
      t1 = space();
    },
    m(target, anchor) {
      insert(target, li, anchor);
      append(li, t0);
      append(li, t1);
    },
    p(ctx2, dirty) {
      if (dirty & /*names*/
      1 && t0_value !== (t0_value = /*name*/
      ctx2[6] + ""))
        set_data(t0, t0_value);
    },
    d(detaching) {
      if (detaching) {
        detach(li);
      }
    }
  };
}
function create_fragment5(ctx) {
  let div;
  let form;
  let p0;
  let t3;
  let ul;
  let t4;
  let p1;
  let t6;
  let button0;
  let t8;
  let button1;
  let mounted;
  let dispose;
  let each_value = ensure_array_like(
    /*names*/
    ctx[0]
  );
  let each_blocks = [];
  for (let i = 0; i < each_value.length; i += 1) {
    each_blocks[i] = create_each_block5(get_each_context5(ctx, each_value, i));
  }
  return {
    c() {
      div = element("div");
      form = element("form");
      p0 = element("p");
      p0.textContent = `The following ${/*word*/
      ctx[4]} will be removed:`;
      t3 = space();
      ul = element("ul");
      for (let i = 0; i < each_blocks.length; i += 1) {
        each_blocks[i].c();
      }
      t4 = space();
      p1 = element("p");
      p1.textContent = "Are you sure you wish to proceed?";
      t6 = space();
      button0 = element("button");
      button0.textContent = "Delete";
      t8 = space();
      button1 = element("button");
      button1.textContent = "Cancel";
      attr(button0, "class", "mod-warning");
      attr(button0, "type", "submit");
    },
    m(target, anchor) {
      insert(target, div, anchor);
      append(div, form);
      append(form, p0);
      append(form, t3);
      append(form, ul);
      for (let i = 0; i < each_blocks.length; i += 1) {
        if (each_blocks[i]) {
          each_blocks[i].m(ul, null);
        }
      }
      append(form, t4);
      append(form, p1);
      append(form, t6);
      append(form, button0);
      append(form, t8);
      append(form, button1);
      ctx[5](button1);
      if (!mounted) {
        dispose = [
          listen(button1, "click", function() {
            if (is_function(
              /*cancel*/
              ctx[2]
            ))
              ctx[2].apply(this, arguments);
          }),
          listen(form, "submit", prevent_default(function() {
            if (is_function(
              /*submission*/
              ctx[1]
            ))
              ctx[1].apply(this, arguments);
          }))
        ];
        mounted = true;
      }
    },
    p(new_ctx, [dirty]) {
      ctx = new_ctx;
      if (dirty & /*names*/
      1) {
        each_value = ensure_array_like(
          /*names*/
          ctx[0]
        );
        let i;
        for (i = 0; i < each_value.length; i += 1) {
          const child_ctx = get_each_context5(ctx, each_value, i);
          if (each_blocks[i]) {
            each_blocks[i].p(child_ctx, dirty);
          } else {
            each_blocks[i] = create_each_block5(child_ctx);
            each_blocks[i].c();
            each_blocks[i].m(ul, null);
          }
        }
        for (; i < each_blocks.length; i += 1) {
          each_blocks[i].d(1);
        }
        each_blocks.length = each_value.length;
      }
    },
    i: noop,
    o: noop,
    d(detaching) {
      if (detaching) {
        detach(div);
      }
      destroy_each(each_blocks, detaching);
      ctx[5](null);
      mounted = false;
      run_all(dispose);
    }
  };
}
function instance5($$self, $$props, $$invalidate) {
  let { names = ["test", "test2"] } = $$props;
  let { submission } = $$props;
  let { cancel } = $$props;
  let btnCancel;
  const word = names.length > 1 ? "properties" : "property";
  onMount(() => {
    btnCancel.focus();
  });
  function button1_binding($$value) {
    binding_callbacks[$$value ? "unshift" : "push"](() => {
      btnCancel = $$value;
      $$invalidate(3, btnCancel);
    });
  }
  $$self.$$set = ($$props2) => {
    if ("names" in $$props2)
      $$invalidate(0, names = $$props2.names);
    if ("submission" in $$props2)
      $$invalidate(1, submission = $$props2.submission);
    if ("cancel" in $$props2)
      $$invalidate(2, cancel = $$props2.cancel);
  };
  return [names, submission, cancel, btnCancel, word, button1_binding];
}
var RemoveConfirmForm = class extends SvelteComponent {
  constructor(options) {
    super();
    init(this, options, instance5, create_fragment5, safe_not_equal, { names: 0, submission: 1, cancel: 2 });
  }
};
var RemoveConfirmForm_default = RemoveConfirmForm;

// src/RemoveConfirmModal.ts
var RemoveConfirmModal = class extends import_obsidian4.Modal {
  constructor(app, names, submission) {
    super(app);
    this.names = names;
    this.submission = submission;
  }
  onSubmit() {
    this.submission(true);
    this.close();
  }
  onCancel() {
    this.submission(false);
    this.close();
  }
  onOpen() {
    if (!this.names || this.names.length === 0) {
      new import_obsidian4.Notice("Please check at least one property to remove.");
      this.close();
    }
    this.titleEl.createEl("h2", { text: "Remove Properties" });
    this.component = new RemoveConfirmForm_default({
      target: this.contentEl,
      props: {
        names: this.names,
        submission: this.onSubmit.bind(this),
        cancel: this.onCancel.bind(this)
      }
    });
  }
};

// src/RemoveModal.ts
var RemoveModal = class extends import_obsidian5.Modal {
  constructor(app, names, submission) {
    if (!names || names.length === 0) {
      new import_obsidian5.Notice("No properties to remove");
      return;
    }
    super(app);
    this.names = names;
    this.submission = submission;
  }
  onConfirm(bool) {
    if (bool) {
      this.submission(this.props);
      this.close();
    }
  }
  onSubmit(props) {
    this.props = props;
    new RemoveConfirmModal(
      this.app,
      this.props,
      this.onConfirm.bind(this)
    ).open();
  }
  onOpen() {
    this.titleEl.createEl("h2", { text: "Remove Properties" });
    this.component = new RemovePropForm_default({
      target: this.contentEl,
      props: {
        names: this.names,
        submission: this.onSubmit.bind(this)
      }
    });
  }
};

// src/frontmatter.ts
function addProperties(app, file, props, overwrite) {
  let propCache = app.metadataCache.getAllPropertyInfos();
  app.fileManager.processFrontMatter(file, (frontmatter) => {
    for (const [key, value] of props) {
      console.log(value);
      if (!frontmatter[key] || overwrite) {
        frontmatter[key] = value.data;
        continue;
      }
      let type1 = value.type;
      let type2 = propCache[key.toLowerCase()].type;
      if (canBeAppended(type1, type2)) {
        if (frontmatter[key] === value.data)
          continue;
        if (!value.data)
          continue;
        let arr = mergeIntoArrays(frontmatter[key], value.data);
        frontmatter[key] = arr;
        continue;
      } else {
        frontmatter[key] = value.data;
        continue;
      }
    }
  });
}
async function addPropToSet(app, set, file) {
  await app.fileManager.processFrontMatter(file, (frontmatter) => {
    for (const key in frontmatter) {
      console.log({ key });
      set.add(key);
    }
  });
  return set;
}
function removeProperties(app, file, props) {
  app.fileManager.processFrontMatter(file, (frontmatter) => {
    for (const prop of props) {
      frontmatter[prop] = void 0;
    }
  });
}
function canBeAppended(str1, str2) {
  let arr = ["number", "date", "datetime", "checkbox"];
  if (arr.includes(str1) || arr.includes(str2))
    return false;
  return true;
}
function mergeIntoArrays(...args) {
  const arrays = args.map((arg) => Array.isArray(arg) ? arg : [arg]);
  const flattened = arrays.flat();
  const unique = [...new Set(flattened)];
  return unique;
}

// src/main.ts
var defaultSettings = {
  overwrite: false,
  recursive: true,
  delimiter: ",",
  defaultPropPath: ""
};
var MultiPropPlugin = class extends import_obsidian6.Plugin {
  async loadSettings() {
    this.settings = Object.assign({}, defaultSettings, await this.loadData());
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
  async changeOverwrite(bool) {
    this.settings.overwrite = bool;
    await this.saveSettings();
  }
  async onload() {
    await this.loadSettings();
    this.addSettingTab(new SettingTab(this.app, this));
    this.registerEvent(
      this.app.workspace.on("file-menu", (menu, folder) => {
        if (folder instanceof import_obsidian6.TFolder) {
          menu.addItem((item) => {
            item.setIcon("archive").setTitle("Add props to folder's notes").onClick(() => this.createPropModal(folder));
          });
        }
      })
    );
    this.registerEvent(
      this.app.workspace.on("file-menu", (menu, folder) => {
        if (folder instanceof import_obsidian6.TFolder) {
          menu.addItem((item) => {
            item.setIcon("archive").setTitle("Remove props from folder's notes").onClick(async () => this.createRemoveModal(folder));
          });
        }
      })
    );
    this.registerEvent(
      this.app.workspace.on("files-menu", (menu, files) => {
        menu.addItem((item) => {
          item.setIcon("archive").setTitle("Add props to selected files").onClick(() => this.createPropModal(files));
        });
      })
    );
    this.registerEvent(
      this.app.workspace.on("files-menu", (menu, files) => {
        menu.addItem((item) => {
          item.setIcon("archive").setTitle("Remove props from selected files").onClick(async () => this.createRemoveModal(files));
        });
      })
    );
    this.registerEvent(
      this.app.workspace.on("search:results-menu", (menu, leaf) => {
        menu.addItem((item) => {
          item.setIcon("archive").setTitle("Add props to search results").onClick(() => {
            let files = this.getFilesFromSearch(leaf);
            if (!files.length) {
              new import_obsidian6.Notice("No files to add properties to.", 4e3);
              return;
            }
            this.createPropModal(files);
          });
        });
      })
    );
    this.registerEvent(
      this.app.workspace.on("search:results-menu", (menu, leaf) => {
        menu.addItem((item) => {
          item.setIcon("archive").setTitle("Remove props from search results").onClick(async () => {
            let files = this.getFilesFromSearch(leaf);
            if (!files.length) {
              new import_obsidian6.Notice("No files to remove properties from.", 4e3);
              return;
            }
            this.createRemoveModal(files);
          });
        });
      })
    );
  }
  async getPropsFromFolder(folder, names) {
    for (let obj of folder.children) {
      if (obj instanceof import_obsidian6.TFile && obj.extension === "md") {
        names = await addPropToSet(this.app, names, obj);
      }
      if (obj instanceof import_obsidian6.TFolder) {
        if (this.settings.recursive) {
          await this.getPropsFromFolder(obj, names);
        }
      }
    }
    return [...names].sort();
  }
  async getPropsFromFiles(files, names) {
    for (let file of files) {
      if (file instanceof import_obsidian6.TFile && file.extension === "md") {
        names = await addPropToSet(this.app, names, file);
      }
    }
    return [...names];
  }
  /** Iterates through all files in a folder and runs callback on each file. */
  searchFolders(folder, callback) {
    for (let obj of folder.children) {
      if (obj instanceof import_obsidian6.TFolder) {
        if (this.settings.recursive) {
          this.searchFolders(obj, callback);
        }
      }
      if (obj instanceof import_obsidian6.TFile && obj.extension === "md") {
        callback(obj);
      }
    }
  }
  /** Iterates through selection of files and runs a given callback function on that file. */
  searchFiles(files, callback) {
    for (let file of files) {
      if (file instanceof import_obsidian6.TFile && file.extension === "md") {
        callback(file);
      }
    }
  }
  /** Get all files from a search result. */
  getFilesFromSearch(leaf) {
    let files = [];
    leaf.dom.vChildren.children.forEach((e) => {
      files.push(e.file);
    });
    return files;
  }
  /** Create modal for adding properties.
   * Will call a different function depending on whether files or a folder is used. */
  createPropModal(iterable) {
    let iterateFunc;
    this.app.vault.getAllLoadedFiles;
    if (iterable instanceof import_obsidian6.TFolder) {
      iterateFunc = (props) => this.searchFolders(iterable, this.addPropsCallback(props));
    } else {
      iterateFunc = (props) => this.searchFiles(iterable, this.addPropsCallback(props));
    }
    let defaultProps;
    if (!this.settings.defaultPropPath) {
      defaultProps = [{ name: "", value: "", type: "text" }];
    } else {
      try {
        const file = this.app.vault.getAbstractFileByPath(
          `${this.settings.defaultPropPath}.md`
        );
        let tmp = this.readYamlProperties(file);
        if (tmp === void 0)
          throw Error("Undefined path.");
        defaultProps = tmp;
      } catch (e) {
        new import_obsidian6.Notice(
          `${e}.  Check if you entered a valid path in the Default Props File setting.`,
          1e4
        );
        defaultProps = [];
      }
    }
    new PropModal(
      this.app,
      iterateFunc,
      this.settings.overwrite,
      this.settings.delimiter,
      defaultProps,
      this.changeOverwrite.bind(this)
    ).open();
  }
  /** Create modal for removing properties.
   * Will call a different function depending on whether files or a folder is used. */
  async createRemoveModal(iterable) {
    let names;
    let iterateFunc;
    if (iterable instanceof import_obsidian6.TFolder) {
      names = await this.getPropsFromFolder(iterable, /* @__PURE__ */ new Set());
      iterateFunc = (props) => this.searchFolders(iterable, this.removePropsCallback(props));
    } else {
      names = await this.getPropsFromFiles(iterable, /* @__PURE__ */ new Set());
      iterateFunc = (props) => this.searchFiles(iterable, this.removePropsCallback(props));
    }
    if (names.length === 0) {
      new import_obsidian6.Notice("No properties to remove", 4e3);
      return;
    }
    const sortedNames = [...names].sort(
      (a, b) => a.toLowerCase() > b.toLowerCase() ? 1 : -1
    );
    new RemoveModal(this.app, sortedNames, iterateFunc).open();
  }
  /** Read through a given file and get name/value of props.
   *  Revised from https://forum.obsidian.md/t/how-to-programmatically-access-a-files-properties-types/77826/4.
   */
  readYamlProperties(file) {
    const metadata = this.app.metadataCache.getFileCache(file);
    const frontmatter = metadata == null ? void 0 : metadata.frontmatter;
    console.log({ frontmatter });
    if (!frontmatter) {
      new import_obsidian6.Notice("Not a valid Props template.", 4e3);
      return;
    }
    const allPropsWithType = this.app.metadataCache.getAllPropertyInfos();
    let result = [];
    for (let [key, value] of Object.entries(frontmatter)) {
      const keyLower = key.toLowerCase();
      const obj = {
        name: key,
        value,
        type: allPropsWithType[keyLower].type
      };
      result.push(obj);
    }
    return result;
  }
  /** Callback function to run addProperties inside iterative functions.*/
  addPropsCallback(props) {
    return (file) => {
      addProperties(this.app, file, props, this.settings.overwrite);
    };
  }
  /** Callback function to run removeProperties inside iterative functions. */
  removePropsCallback(props) {
    return (file) => {
      removeProperties(this.app, file, props);
    };
  }
};

/* nosourcemap */