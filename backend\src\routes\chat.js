import express from 'express';
import { body, validationResult } from 'express-validator';

const router = express.Router();

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    });
  }
  next();
};

// POST /api/chat/completions - Send chat completion request
router.post('/completions',
  [
    body('messages').isArray().withMessage('Messages must be an array'),
    body('messages.*.content').notEmpty().withMessage('Message content is required'),
    body('messages.*.role').isIn(['user', 'assistant', 'system']).withMessage('Invalid message role'),
    body('model').optional().isString().withMessage('Model must be a string'),
    body('temperature').optional().isFloat({ min: 0, max: 2 }).withMessage('Temperature must be between 0 and 2'),
    body('max_tokens').optional().isInt({ min: 1 }).withMessage('Max tokens must be a positive integer'),
    body('stream').optional().isBoolean().withMessage('Stream must be a boolean')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { messages, model, temperature, max_tokens, stream, ...options } = req.body;
      const lmstudioService = req.app.locals.lmstudioService;
      const logger = req.app.locals.logger;
      
      logger.info('Chat completion request', {
        messageCount: messages.length,
        model,
        temperature,
        max_tokens,
        stream
      });

      const requestOptions = {
        model,
        temperature,
        maxTokens: max_tokens,
        stream,
        ...options
      };

      if (stream) {
        // Set up Server-Sent Events for streaming
        res.writeHead(200, {
          'Content-Type': 'text/plain',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Cache-Control'
        });

        try {
          await lmstudioService.streamChatCompletion(
            messages,
            requestOptions,
            (chunk, data) => {
              res.write(`data: ${JSON.stringify(data)}\n\n`);
            }
          );
          
          res.write('data: [DONE]\n\n');
          res.end();
        } catch (error) {
          logger.logError(error, { endpoint: 'POST /api/chat/completions (streaming)' });
          res.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
          res.end();
        }
      } else {
        // Non-streaming response
        const response = await lmstudioService.sendChatCompletion(messages, requestOptions);
        
        logger.info('Chat completion successful', {
          responseLength: response.choices?.[0]?.message?.content?.length || 0
        });
        
        res.json(response);
      }
    } catch (error) {
      const logger = req.app.locals.logger;
      logger.logError(error, { endpoint: 'POST /api/chat/completions' });
      
      res.status(500).json({
        error: 'Chat completion failed',
        message: error.message
      });
    }
  }
);

// POST /api/chat/stream - Alternative streaming endpoint
router.post('/stream',
  [
    body('messages').isArray().withMessage('Messages must be an array'),
    body('model').optional().isString().withMessage('Model must be a string')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { messages, model, ...options } = req.body;
      const lmstudioService = req.app.locals.lmstudioService;
      const logger = req.app.locals.logger;
      
      logger.info('Streaming chat request', { messageCount: messages.length, model });

      // Set up streaming response
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
      });

      let fullResponse = '';
      
      await lmstudioService.streamChatCompletion(
        messages,
        { model, stream: true, ...options },
        (chunk, data) => {
          fullResponse += chunk;
          res.write(`data: ${JSON.stringify({
            type: 'chunk',
            content: chunk,
            fullContent: fullResponse
          })}\n\n`);
        }
      );
      
      res.write(`data: ${JSON.stringify({
        type: 'done',
        fullContent: fullResponse
      })}\n\n`);
      
      res.end();
    } catch (error) {
      const logger = req.app.locals.logger;
      logger.logError(error, { endpoint: 'POST /api/chat/stream' });
      
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: error.message
      })}\n\n`);
      res.end();
    }
  }
);

// GET /api/chat/models - Get available chat models
router.get('/models', async (req, res) => {
  try {
    const lmstudioService = req.app.locals.lmstudioService;
    const logger = req.app.locals.logger;
    
    logger.info('Fetching available chat models');
    
    const [downloadedModels, loadedModels] = await Promise.all([
      lmstudioService.getDownloadedModels(),
      lmstudioService.getLoadedModels()
    ]);

    // Filter for chat-capable models
    const chatModels = downloadedModels.filter(model => 
      model.type === 'chat' || 
      model.name.toLowerCase().includes('chat') ||
      model.name.toLowerCase().includes('instruct')
    );

    const loadedModelIds = new Set(loadedModels.map(m => m.id));
    
    const enrichedChatModels = chatModels.map(model => ({
      ...model,
      loaded: loadedModelIds.has(model.id),
      suitable_for_chat: true
    }));

    res.json({
      models: enrichedChatModels,
      loaded: loadedModels,
      count: enrichedChatModels.length
    });
  } catch (error) {
    const logger = req.app.locals.logger;
    logger.logError(error, { endpoint: 'GET /api/chat/models' });
    
    res.status(500).json({
      error: 'Failed to fetch chat models',
      message: error.message
    });
  }
});

// POST /api/chat/test - Test chat functionality
router.post('/test', async (req, res) => {
  try {
    const lmstudioService = req.app.locals.lmstudioService;
    const logger = req.app.locals.logger;
    
    logger.info('Testing chat functionality');
    
    // Simple test message
    const testMessages = [
      { role: 'user', content: 'Say "Hello from LM Studio WebUI!" if you can hear me.' }
    ];

    const response = await lmstudioService.sendChatCompletion(testMessages, {
      temperature: 0.7,
      max_tokens: 50
    });

    logger.info('Chat test successful');
    
    res.json({
      success: true,
      message: 'Chat functionality is working',
      response: response.choices?.[0]?.message?.content || 'No response content',
      full_response: response
    });
  } catch (error) {
    const logger = req.app.locals.logger;
    logger.logError(error, { endpoint: 'POST /api/chat/test' });
    
    res.status(500).json({
      success: false,
      error: 'Chat test failed',
      message: error.message
    });
  }
});

export default router; 