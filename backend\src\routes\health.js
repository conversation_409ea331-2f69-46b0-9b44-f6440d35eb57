import express from 'express';

const router = express.Router();

// GET /api/health - Basic health check
router.get('/', async (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '2.0.0'
    };

    res.json(health);
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/detailed - Detailed health check including LM Studio
router.get('/detailed', async (req, res) => {
  try {
    const lmstudioService = req.app.locals.lmstudioService;
    const logger = req.app.locals.logger;
    
    // Check LM Studio status
    let lmstudioStatus = null;
    try {
      lmstudioStatus = await lmstudioService.checkLMStudioStatus();
    } catch (error) {
      lmstudioStatus = {
        running: false,
        server: false,
        error: error.message
      };
    }

    // Get model counts
    let modelInfo = { downloaded: 0, loaded: 0 };
    try {
      const [downloaded, loaded] = await Promise.all([
        lmstudioService.getDownloadedModels(),
        lmstudioService.getLoadedModels()
      ]);
      modelInfo = {
        downloaded: downloaded.length,
        loaded: loaded.length
      };
    } catch (error) {
      logger.warn('Failed to get model info for health check', { error: error.message });
    }

    const health = {
      status: lmstudioStatus.running ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '2.0.0',
      services: {
        lmstudio: lmstudioStatus,
        models: modelInfo
      }
    };

    const statusCode = lmstudioStatus.running ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    const logger = req.app.locals.logger;
    logger.logError(error, { endpoint: 'GET /api/health/detailed' });
    
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/lmstudio - LM Studio specific health check
router.get('/lmstudio', async (req, res) => {
  try {
    const lmstudioService = req.app.locals.lmstudioService;
    const logger = req.app.locals.logger;
    
    const status = await lmstudioService.checkLMStudioStatus();
    
    logger.info('LM Studio health check', status);
    
    res.json({
      status: status.running && status.server ? 'healthy' : 'unhealthy',
      lmstudio: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    const logger = req.app.locals.logger;
    logger.logError(error, { endpoint: 'GET /api/health/lmstudio' });
    
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

export default router; 