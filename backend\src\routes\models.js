import express from 'express';
import { body, param, validationResult } from 'express-validator';

const router = express.Router();

// Get the LM Studio service from the app context
const getLMStudioService = (req) => req.app.locals.lmstudioService;
const getLogger = (req) => req.app.locals.logger;

// Validation middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    });
  }
  next();
};

// GET /api/models - Get all available models (loaded + downloadable)
router.get('/', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Fetching all models');
    
    // Get both downloaded and loaded models
    const [downloadedModels, loadedModels, availableModels] = await Promise.all([
      lmstudioService.getDownloadedModels(),
      lmstudioService.getLoadedModels(),
      lmstudioService.getAvailableModels()
    ]);

    // Create a map of loaded model IDs for quick lookup
    const loadedModelIds = new Set(loadedModels.map(m => m.id));
    
    // Mark downloaded models as loaded if they appear in loaded models
    const enrichedDownloadedModels = downloadedModels.map(model => ({
      ...model,
      loaded: loadedModelIds.has(model.id)
    }));

    const response = {
      downloaded: enrichedDownloadedModels,
      loaded: loadedModels,
      available: availableModels,
      summary: {
        totalDownloaded: downloadedModels.length,
        totalLoaded: loadedModels.length,
        totalAvailable: availableModels.length
      }
    };

    logger.info('Models fetched successfully', {
      downloaded: downloadedModels.length,
      loaded: loadedModels.length,
      available: availableModels.length
    });

    res.json(response);
  } catch (error) {
    const logger = getLogger(req);
    logger.logError(error, { endpoint: 'GET /api/models' });
    
    res.status(500).json({
      error: 'Failed to fetch models',
      message: error.message
    });
  }
});

// GET /api/models/downloaded - Get downloaded models only
router.get('/downloaded', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Fetching downloaded models');
    
    const downloadedModels = await lmstudioService.getDownloadedModels();
    const loadedModels = await lmstudioService.getLoadedModels();
    
    const loadedModelIds = new Set(loadedModels.map(m => m.id));
    
    const enrichedModels = downloadedModels.map(model => ({
      ...model,
      loaded: loadedModelIds.has(model.id)
    }));

    res.json({
      models: enrichedModels,
      count: enrichedModels.length
    });
  } catch (error) {
    const logger = getLogger(req);
    logger.logError(error, { endpoint: 'GET /api/models/downloaded' });
    
    res.status(500).json({
      error: 'Failed to fetch downloaded models',
      message: error.message
    });
  }
});

// GET /api/models/loaded - Get currently loaded models
router.get('/loaded', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Fetching loaded models');
    
    const loadedModels = await lmstudioService.getLoadedModels();

    res.json({
      models: loadedModels,
      count: loadedModels.length
    });
  } catch (error) {
    const logger = getLogger(req);
    logger.logError(error, { endpoint: 'GET /api/models/loaded' });
    
    res.status(500).json({
      error: 'Failed to fetch loaded models',
      message: error.message
    });
  }
});

// POST /api/models/load - Load a model
router.post('/load',
  [
    body('modelPath').notEmpty().withMessage('Model path is required'),
    body('options.gpu').optional().isFloat({ min: 0, max: 1 }).withMessage('GPU must be between 0 and 1'),
    body('options.contextLength').optional().isInt({ min: 1 }).withMessage('Context length must be a positive integer'),
    body('options.identifier').optional().isString().withMessage('Identifier must be a string')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { modelPath, options = {} } = req.body;
      const lmstudioService = getLMStudioService(req);
      const logger = getLogger(req);
      
      logger.info('Loading model', { modelPath, options });
      
      const startTime = Date.now();
      const success = await lmstudioService.loadModel(modelPath, options);
      const duration = Date.now() - startTime;
      
      if (success) {
        logger.logModelOperation('load', modelPath, true, { duration, options });
        res.json({
          success: true,
          message: `Model ${modelPath} loaded successfully`,
          modelPath,
          loadTime: duration
        });
      } else {
        throw new Error('Failed to load model');
      }
    } catch (error) {
      const logger = getLogger(req);
      logger.logModelOperation('load', req.body.modelPath, false, { error: error.message });
      
      res.status(500).json({
        error: 'Failed to load model',
        message: error.message,
        modelPath: req.body.modelPath
      });
    }
  }
);

// POST /api/models/unload - Unload a specific model
router.post('/unload',
  [
    body('modelPath').notEmpty().withMessage('Model path is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { modelPath } = req.body;
      const lmstudioService = getLMStudioService(req);
      const logger = getLogger(req);
      
      logger.info('Unloading model', { modelPath });
      
      const success = await lmstudioService.unloadModel(modelPath);
      
      if (success) {
        logger.logModelOperation('unload', modelPath, true);
        res.json({
          success: true,
          message: `Model ${modelPath} unloaded successfully`,
          modelPath
        });
      } else {
        throw new Error('Failed to unload model');
      }
    } catch (error) {
      const logger = getLogger(req);
      logger.logModelOperation('unload', req.body.modelPath, false, { error: error.message });
      
      res.status(500).json({
        error: 'Failed to unload model',
        message: error.message,
        modelPath: req.body.modelPath
      });
    }
  }
);

// POST /api/models/unload-all - Unload all models
router.post('/unload-all', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Unloading all models');
    
    const success = await lmstudioService.unloadAllModels();
    
    if (success) {
      logger.logModelOperation('unload-all', 'all', true);
      res.json({
        success: true,
        message: 'All models unloaded successfully'
      });
    } else {
      throw new Error('Failed to unload all models');
    }
  } catch (error) {
    const logger = getLogger(req);
    logger.logModelOperation('unload-all', 'all', false, { error: error.message });
    
    res.status(500).json({
      error: 'Failed to unload all models',
      message: error.message
    });
  }
});

// GET /api/models/status - Get LM Studio status and model information
router.get('/status', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Checking LM Studio status');
    
    const status = await lmstudioService.checkLMStudioStatus();
    
    res.json({
      lmstudio: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    const logger = getLogger(req);
    logger.logError(error, { endpoint: 'GET /api/models/status' });
    
    res.status(500).json({
      error: 'Failed to check LM Studio status',
      message: error.message
    });
  }
});

// POST /api/models/start-server - Start LM Studio server
router.post('/start-server', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Starting LM Studio server');
    
    const success = await lmstudioService.startLMStudio();
    
    if (success) {
      logger.info('LM Studio server started successfully');
      res.json({
        success: true,
        message: 'LM Studio server started successfully'
      });
    } else {
      throw new Error('Failed to start LM Studio server');
    }
  } catch (error) {
    const logger = getLogger(req);
    logger.logError(error, { endpoint: 'POST /api/models/start-server' });
    
    res.status(500).json({
      error: 'Failed to start LM Studio server',
      message: error.message
    });
  }
});

// POST /api/models/stop-server - Stop LM Studio server
router.post('/stop-server', async (req, res) => {
  try {
    const lmstudioService = getLMStudioService(req);
    const logger = getLogger(req);
    
    logger.info('Stopping LM Studio server');
    
    const success = await lmstudioService.stopLMStudio();
    
    if (success) {
      logger.info('LM Studio server stopped successfully');
      res.json({
        success: true,
        message: 'LM Studio server stopped successfully'
      });
    } else {
      throw new Error('Failed to stop LM Studio server');
    }
  } catch (error) {
    const logger = getLogger(req);
    logger.logError(error, { endpoint: 'POST /api/models/stop-server' });
    
    res.status(500).json({
      error: 'Failed to stop LM Studio server',
      message: error.message
    });
  }
});

// GET /api/models/:modelPath/info - Get detailed information about a specific model
router.get('/:modelPath/info', 
  [
    param('modelPath').notEmpty().withMessage('Model path is required')
  ],
  handleValidationErrors,
  async (req, res) => {
    try {
      const { modelPath } = req.params;
      const lmstudioService = getLMStudioService(req);
      const logger = getLogger(req);
      
      logger.info('Getting model info', { modelPath });
      
      // Get model from downloaded models list
      const downloadedModels = await lmstudioService.getDownloadedModels();
      const loadedModels = await lmstudioService.getLoadedModels();
      
      const model = downloadedModels.find(m => m.id === modelPath || m.path === modelPath);
      const loadedModel = loadedModels.find(m => m.id === modelPath || m.path === modelPath);
      
      if (!model) {
        return res.status(404).json({
          error: 'Model not found',
          modelPath
        });
      }
      
      const modelInfo = {
        ...model,
        loaded: !!loadedModel,
        loadedInfo: loadedModel || null
      };
      
      res.json(modelInfo);
    } catch (error) {
      const logger = getLogger(req);
      logger.logError(error, { endpoint: 'GET /api/models/:modelPath/info', modelPath: req.params.modelPath });
      
      res.status(500).json({
        error: 'Failed to get model information',
        message: error.message,
        modelPath: req.params.modelPath
      });
    }
  }
);

export default router; 