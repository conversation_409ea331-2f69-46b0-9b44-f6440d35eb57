#!/usr/bin/env node

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

class LMStudioStartup {
  constructor() {
    this.platform = process.platform;
    this.cliPaths = this.getLMStudioCliPaths();
    this.maxRetries = 5;
    this.retryDelay = 2000;
  }

  getLMStudioCliPaths() {
    const homeDir = process.env.HOME || process.env.USERPROFILE;
    
    switch (this.platform) {
      case 'win32':
        return [
          path.join(homeDir, '.lmstudio', 'bin', 'lms.exe'),
          path.join(process.env.LOCALAPPDATA || '', 'LM Studio', 'bin', 'lms.exe'),
          'lms.exe',
          'lms'
        ];
      case 'darwin':
        return [
          path.join(homeDir, '.lmstudio', 'bin', 'lms'),
          '/Applications/LM Studio.app/Contents/Resources/bin/lms',
          'lms'
        ];
      case 'linux':
        return [
          path.join(homeDir, '.lmstudio', 'bin', 'lms'),
          '/usr/local/bin/lms',
          'lms'
        ];
      default:
        return ['lms'];
    }
  }

  async findLMStudioCli() {
    for (const cliPath of this.cliPaths) {
      try {
        const { stdout } = await execAsync(`"${cliPath}" version`);
        if (stdout.includes('lms')) {
          console.log(`✅ Found LM Studio CLI at: ${cliPath}`);
          return cliPath;
        }
      } catch (error) {
        continue;
      }
    }
    return null;
  }

  async executeLMSCommand(command, args = []) {
    const cliPath = await this.findLMStudioCli();
    if (!cliPath) {
      throw new Error('LM Studio CLI not found');
    }

    const fullCommand = `"${cliPath}" ${command} ${args.join(' ')}`;
    console.log(`🔧 Executing: ${fullCommand}`);

    try {
      const { stdout, stderr } = await execAsync(fullCommand);
      if (stderr && !stderr.includes('INFO')) {
        console.warn(`⚠️  Warning: ${stderr}`);
      }
      return stdout;
    } catch (error) {
      throw new Error(`LMS command failed: ${error.message}`);
    }
  }

  async checkLMStudioStatus() {
    try {
      // Check if server is running by trying to connect
      const response = await fetch('http://localhost:1234/v1/models', { 
        signal: AbortSignal.timeout(5000) 
      });
      return {
        running: true,
        server: response.ok,
        apiAvailable: response.ok
      };
    } catch (error) {
      // Check if LM Studio process is running
      try {
        const status = await this.executeLMSCommand('status');
        const isRunning = status.includes('running') || status.includes('LM Studio is running');
        return {
          running: isRunning,
          server: false,
          apiAvailable: false
        };
      } catch (statusError) {
        return {
          running: false,
          server: false,
          apiAvailable: false
        };
      }
    }
  }

  async startLMStudioServer() {
    console.log('🚀 Starting LM Studio server...');
    
    try {
      await this.executeLMSCommand('server', ['start']);
      
      // Wait for server to become available
      for (let i = 0; i < 30; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        try {
          const response = await fetch('http://localhost:1234/v1/models', { 
            signal: AbortSignal.timeout(2000) 
          });
          if (response.ok) {
            console.log('✅ LM Studio server is ready!');
            return true;
          }
        } catch (error) {
          // Continue waiting
        }
        
        process.stdout.write('.');
      }
      
      throw new Error('Server failed to start within timeout');
    } catch (error) {
      console.error(`❌ Failed to start LM Studio server: ${error.message}`);
      throw error;
    }
  }

  async ensureLMStudioReady() {
    console.log('🔍 Checking LM Studio status...');
    
    const status = await this.checkLMStudioStatus();
    
    if (status.apiAvailable) {
      console.log('✅ LM Studio is already running and API is available');
      return true;
    }
    
    if (status.running && !status.server) {
      console.log('🔧 LM Studio is running but server is not started');
      await this.startLMStudioServer();
      return true;
    }
    
    if (!status.running) {
      console.log('❌ LM Studio is not running');
      console.log('📝 Please start LM Studio manually first, then run this script again');
      console.log('');
      console.log('Steps:');
      console.log('1. Start LM Studio application');
      console.log('2. Load a model in LM Studio');
      console.log('3. Enable "Serve on Local Network" in LM Studio settings');
      console.log('4. Run this script again');
      
      return false;
    }
    
    return true;
  }

  async getModelInfo() {
    try {
      const [downloadedOutput, loadedOutput] = await Promise.all([
        this.executeLMSCommand('ls'),
        this.executeLMSCommand('ps')
      ]);
      
      const downloadedCount = downloadedOutput.split('\n').filter(line => 
        line.trim() && !line.includes('Model') && !line.includes('---')
      ).length;
      
      const loadedCount = loadedOutput.split('\n').filter(line => 
        line.trim() && !line.includes('Model') && !line.includes('---')
      ).length;
      
      return { downloadedCount, loadedCount };
    } catch (error) {
      console.warn('⚠️  Could not get model information:', error.message);
      return { downloadedCount: 0, loadedCount: 0 };
    }
  }

  async run() {
    console.log('🎯 LM Studio WebUI - Startup Check');
    console.log('===================================');
    
    try {
      // Check if LM Studio CLI is available
      const cliPath = await this.findLMStudioCli();
      if (!cliPath) {
        console.log('❌ LM Studio CLI not found!');
        console.log('');
        console.log('Please install LM Studio and ensure the CLI is available:');
        if (this.platform === 'win32') {
          console.log('Run: cmd /c %USERPROFILE%/.lmstudio/bin/lms.exe bootstrap');
        } else {
          console.log('Run: ~/.lmstudio/bin/lms bootstrap');
        }
        process.exit(1);
      }
      
      // Ensure LM Studio is ready
      const ready = await this.ensureLMStudioReady();
      if (!ready) {
        process.exit(1);
      }
      
      // Get model information
      const modelInfo = await this.getModelInfo();
      
      console.log('');
      console.log('📊 Model Status:');
      console.log(`   Downloaded models: ${modelInfo.downloadedCount}`);
      console.log(`   Loaded models: ${modelInfo.loadedCount}`);
      
      if (modelInfo.loadedCount === 0) {
        console.log('');
        console.log('⚠️  No models are currently loaded!');
        console.log('💡 Tip: Load a model in LM Studio for better chat experience');
      }
      
      console.log('');
      console.log('🎉 LM Studio is ready! Starting WebUI...');
      console.log('');
      
      return true;
    } catch (error) {
      console.error('❌ Startup failed:', error.message);
      process.exit(1);
    }
  }
}

// Run startup check if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const startup = new LMStudioStartup();
  startup.run().then(() => {
    console.log('✅ Startup check completed successfully');
  }).catch(error => {
    console.error('❌ Startup check failed:', error.message);
    process.exit(1);
  });
}

export { LMStudioStartup }; 