#!/usr/bin/env node

import { PortManager } from '../utils/port-manager.js';
import { Logger } from '../utils/logger.js';

const logger = new Logger();
const portManager = new PortManager(logger);

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case 'status':
      case 'summary':
        await showPortSummary();
        break;
        
      case 'cleanup':
        await cleanupDevPorts();
        break;
        
      case 'kill-node':
        await killNodeProcesses();
        break;
        
      case 'kill-port':
        const port = parseInt(args[1]);
        if (!port) {
          console.error('Usage: npm run port-cleanup kill-port <port>');
          process.exit(1);
        }
        await killPortProcesses(port);
        break;
        
      case 'find-free':
        const preferredPort = parseInt(args[1]) || 8000;
        await findFreePort(preferredPort);
        break;
        
      case 'help':
      default:
        showHelp();
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

async function showPortSummary() {
  console.log('🔍 Scanning development ports...\n');
  
  const usedPorts = await portManager.getPortSummary();
  
  if (usedPorts.length === 0) {
    console.log('✅ No processes found on development ports');
    return;
  }
  
  console.log(`📊 Found ${usedPorts.length} ports in use:\n`);
  
  for (const { port, processes } of usedPorts) {
    console.log(`Port ${port}:`);
    for (const process of processes) {
      console.log(`  └─ ${process.name} (PID: ${process.pid})`);
    }
    console.log();
  }
}

async function cleanupDevPorts() {
  console.log('🧹 Cleaning up development ports...\n');
  
  const cleaned = await portManager.cleanupDevPorts();
  
  if (cleaned.length === 0) {
    console.log('✅ No cleanup needed - all development ports are clean');
    return;
  }
  
  console.log(`🗑️  Cleaned up ${cleaned.length} ports:\n`);
  
  for (const { port, processes } of cleaned) {
    console.log(`Port ${port}:`);
    for (const process of processes) {
      console.log(`  └─ Killed ${process.name} (PID: ${process.pid})`);
    }
    console.log();
  }
  
  console.log('✅ Cleanup complete!');
}

async function killNodeProcesses() {
  console.log('🔪 Killing all Node.js processes (except current)...\n');
  
  const killed = await portManager.killNodeProcesses();
  
  if (killed.length === 0) {
    console.log('✅ No Node.js processes to kill');
    return;
  }
  
  console.log(`🗑️  Killed ${killed.length} Node.js processes:\n`);
  
  for (const process of killed) {
    console.log(`  └─ ${process.name} (PID: ${process.pid})`);
  }
  
  console.log('\n✅ All Node.js processes killed!');
}

async function killPortProcesses(port) {
  console.log(`🔪 Killing processes on port ${port}...\n`);
  
  const killed = await portManager.killPortProcesses(port, true);
  
  if (killed.length === 0) {
    console.log(`✅ No processes found on port ${port}`);
    return;
  }
  
  console.log(`🗑️  Killed ${killed.length} processes:\n`);
  
  for (const process of killed) {
    console.log(`  └─ ${process.name} (PID: ${process.pid})`);
  }
  
  console.log('\n✅ Port cleanup complete!');
}

async function findFreePort(preferredPort) {
  console.log(`🔍 Finding free port starting from ${preferredPort}...\n`);
  
  const freePort = await portManager.findFreePort(preferredPort);
  
  if (freePort === preferredPort) {
    console.log(`✅ Port ${preferredPort} is available!`);
  } else {
    console.log(`⚠️  Port ${preferredPort} is busy`);
    console.log(`✅ Found free port: ${freePort}`);
  }
}

function showHelp() {
  console.log(`
🔧 Port Management Utility

Usage: npm run port-cleanup <command> [options]

Commands:
  status              Show all processes using development ports
  cleanup             Clean up stale Node.js processes on dev ports
  kill-node           Kill all Node.js processes (except current)
  kill-port <port>    Kill all processes using a specific port
  find-free <port>    Find a free port starting from the given port (default: 8000)
  help                Show this help message

Examples:
  npm run port-cleanup status
  npm run port-cleanup cleanup
  npm run port-cleanup kill-port 8000
  npm run port-cleanup find-free 3000

Development ports scanned: 8000-8010, 3000-3010, 5173-5180
`);
}

main().catch(console.error); 