import axios from 'axios';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';

const execAsync = promisify(exec);
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export class LMStudioService {
  constructor(config, logger) {
    this.config = config;
    this.logger = logger;
    this.host = config.get('lmstudio.host') || 'localhost';
    this.port = config.get('lmstudio.port') || 1234;
    this.timeout = config.get('lmstudio.timeout') || 300000; // 5 minutes for reasoning models
    this.retryAttempts = config.get('lmstudio.retryAttempts') || 3;
    this.retryDelay = config.get('lmstudio.retryDelay') || 1000;
    this.baseUrl = `http://${this.host}:${this.port}`;
    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // LM Studio CLI paths for different platforms
    this.cliPaths = this.getLMStudioCliPaths();
    this.isStarting = false;
    this.serverProcess = null;
  }

  getLMStudioCliPaths() {
    const platform = process.platform;
    const homeDir = process.env.HOME || process.env.USERPROFILE;
    
    switch (platform) {
      case 'win32':
        return [
          path.join(homeDir, '.lmstudio', 'bin', 'lms.exe'),
          path.join(process.env.LOCALAPPDATA || '', 'LM Studio', 'bin', 'lms.exe'),
          'lms.exe',
          'lms'
        ];
      case 'darwin':
        return [
          path.join(homeDir, '.lmstudio', 'bin', 'lms'),
          '/Applications/LM Studio.app/Contents/Resources/bin/lms',
          'lms'
        ];
      case 'linux':
        return [
          path.join(homeDir, '.lmstudio', 'bin', 'lms'),
          '/usr/local/bin/lms',
          'lms'
        ];
      default:
        return ['lms'];
    }
  }

  async findLMStudioCli() {
    for (const cliPath of this.cliPaths) {
      try {
        const { stdout } = await execAsync(`"${cliPath}" version`);
        if (stdout.includes('lms')) {
          this.logger.info(`Found LM Studio CLI at: ${cliPath}`);
          return cliPath;
        }
      } catch (error) {
        // Continue trying other paths
        continue;
      }
    }
    
    this.logger.warn('LM Studio CLI not found in standard locations');
    return null;
  }

  async executeLMSCommand(command, args = []) {
    const cliPath = await this.findLMStudioCli();
    if (!cliPath) {
      throw new Error('LM Studio CLI not found. Please install LM Studio and ensure lms is in your PATH.');
    }

    const fullCommand = `"${cliPath}" ${command} ${args.join(' ')}`;
    this.logger.info(`Executing LMS command: ${fullCommand}`);

    try {
      const { stdout, stderr } = await execAsync(fullCommand);
      if (stderr && !stderr.includes('INFO')) {
        this.logger.warn(`LMS command warning: ${stderr}`);
      }
      return stdout;
    } catch (error) {
      this.logger.error(`LMS command failed: ${error.message}`);
      throw new Error(`LM Studio command failed: ${error.message}`);
    }
  }

  async checkLMStudioStatus() {
    try {
      const response = await this.apiClient.get('/v1/models', { timeout: 5000 });
      return {
        running: true,
        server: true,
        models: response.data?.data || []
      };
    } catch (error) {
      // Check if LM Studio process is running
      try {
        const status = await this.executeLMSCommand('status');
        const isRunning = status.includes('running') || status.includes('LM Studio is running');
        return {
          running: isRunning,
          server: false,
          models: []
        };
      } catch (statusError) {
        return {
          running: false,
          server: false,
          models: []
        };
      }
    }
  }

  async startLMStudio() {
    if (this.isStarting) {
      this.logger.info('LM Studio is already starting...');
      return;
    }

    this.isStarting = true;
    this.logger.info('Starting LM Studio...');

    try {
      // First, try to start the server using CLI
      await this.executeLMSCommand('server', ['start']);
      
      // Wait for server to be ready
      let attempts = 0;
      const maxAttempts = 30; // 30 seconds timeout
      
      while (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        const status = await this.checkLMStudioStatus();
        
        if (status.server) {
          this.logger.info('LM Studio server started successfully');
          this.isStarting = false;
          return true;
        }
        
        attempts++;
        this.logger.info(`Waiting for LM Studio server... (${attempts}/${maxAttempts})`);
      }
      
      throw new Error('LM Studio server failed to start within timeout period');
      
    } catch (error) {
      this.logger.error(`Failed to start LM Studio: ${error.message}`);
      this.isStarting = false;
      throw error;
    }
  }

  async stopLMStudio() {
    try {
      this.logger.info('Stopping LM Studio server...');
      await this.executeLMSCommand('server', ['stop']);
      this.logger.info('LM Studio server stopped');
      return true;
    } catch (error) {
      this.logger.error(`Failed to stop LM Studio: ${error.message}`);
      throw error;
    }
  }

  async ensureLMStudioRunning() {
    const status = await this.checkLMStudioStatus();
    
    if (!status.running) {
      this.logger.info('LM Studio is not running. Starting it now...');
      await this.startLMStudio();
    } else if (!status.server) {
      this.logger.info('LM Studio is running but server is not started. Starting server...');
      await this.executeLMSCommand('server', ['start']);
      
      // Wait for server to be ready
      await this.waitForServer();
    } else {
      this.logger.info('LM Studio is running and server is ready');
    }
    
    return true;
  }

  async waitForServer(maxAttempts = 30) {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        await this.apiClient.get('/v1/models', { timeout: 2000 });
        return true;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    throw new Error('LM Studio server failed to become ready');
  }

  async getDownloadedModels() {
    try {
      const output = await this.executeLMSCommand('ls');
      const models = this.parseModelsList(output);
      this.logger.info(`Found ${models.length} downloaded models`);
      return models;
    } catch (error) {
      this.logger.error(`Failed to get downloaded models: ${error.message}`);
      return [];
    }
  }

  parseModelsList(output) {
    const lines = output.split('\n').filter(line => line.trim());
    const models = [];
    
    for (const line of lines) {
      // Skip header lines and empty lines
      if (line.includes('Model') || line.includes('---') || !line.trim()) {
        continue;
      }
      
      // Parse model information
      const parts = line.trim().split(/\s+/);
      if (parts.length >= 1) {
        const modelPath = parts[0];
        const modelName = modelPath.split('/').pop() || modelPath;
        
        models.push({
          id: modelPath,
          name: modelName,
          path: modelPath,
          size: parts[1] || 'Unknown',
          type: this.getModelType(modelName),
          loaded: false
        });
      }
    }
    
    return models;
  }

  getModelType(modelName) {
    const name = modelName.toLowerCase();
    if (name.includes('instruct') || name.includes('chat')) return 'chat';
    if (name.includes('code')) return 'code';
    if (name.includes('embed')) return 'embedding';
    if (name.includes('vision') || name.includes('llava')) return 'vision';
    return 'general';
  }

  async getLoadedModels() {
    try {
      const output = await this.executeLMSCommand('ps');
      const models = this.parseLoadedModels(output);
      this.logger.info(`Found ${models.length} loaded models`);
      return models;
    } catch (error) {
      this.logger.error(`Failed to get loaded models: ${error.message}`);
      return [];
    }
  }

  parseLoadedModels(output) {
    const lines = output.split('\n').filter(line => line.trim());
    const models = [];
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Look for "Identifier:" lines which contain the model ID
      if (trimmedLine.startsWith('Identifier:')) {
        const modelId = trimmedLine.replace('Identifier:', '').trim();
        if (modelId) {
          const model = {
            id: modelId,
            name: modelId.split('/').pop() || modelId,
            path: modelId,
            loaded: true,
            gpu: 'auto'
          };
          models.push(model);
        }
      }
    }
    
    return models;
  }

  async loadModel(modelPath, options = {}) {
    try {
      this.logger.info(`Loading model: ${modelPath}`);
      
      const args = [modelPath, '--yes', '--quiet']; // Add flags to avoid prompts and progress bar issues
      
      if (options.gpu) {
        args.push(`--gpu=${options.gpu}`);
      }
      
      if (options.contextLength) {
        args.push(`--context-length=${options.contextLength}`);
      }
      
      if (options.identifier) {
        args.push(`--identifier="${options.identifier}"`);
      }
      
      await this.executeLMSCommand('load', args);
      this.logger.info(`Model loaded successfully: ${modelPath}`);
      
      // Wait a moment for the model to be fully loaded
      await new Promise(resolve => setTimeout(resolve, 5000)); // Increased wait time
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to load model ${modelPath}: ${error.message}`);
      throw error;
    }
  }

  async unloadModel(modelPath) {
    try {
      this.logger.info(`Unloading model: ${modelPath}`);
      await this.executeLMSCommand('unload', [modelPath]);
      this.logger.info(`Model unloaded successfully: ${modelPath}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to unload model ${modelPath}: ${error.message}`);
      throw error;
    }
  }

  async unloadAllModels() {
    try {
      this.logger.info('Unloading all models');
      await this.executeLMSCommand('unload', ['--all']);
      this.logger.info('All models unloaded successfully');
      return true;
    } catch (error) {
      this.logger.error(`Failed to unload all models: ${error.message}`);
      throw error;
    }
  }

  async getAvailableModels() {
    try {
      await this.ensureLMStudioRunning();
      const response = await this.apiClient.get('/v1/models');
      return response.data?.data || [];
    } catch (error) {
      this.logger.error(`Failed to get available models: ${error.message}`);
      return [];
    }
  }

  async sendChatCompletion(messages, options = {}) {
    try {
      await this.ensureLMStudioRunning();
      
      const requestData = {
        model: options.model || 'loaded-model',
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || -1,
        stream: options.stream || false,
        ...options
      };

      const response = await this.apiClient.post('/v1/chat/completions', requestData);
      return response.data;
    } catch (error) {
      this.logger.error(`Chat completion failed: ${error.message}`);
      throw error;
    }
  }

  async streamChatCompletion(messages, options = {}, onChunk) {
    try {
      await this.ensureLMStudioRunning();
      
      const requestData = {
        model: options.model || 'loaded-model',
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || -1,
        stream: true,
        ...options
      };

      const response = await this.apiClient.post('/v1/chat/completions', requestData, {
        responseType: 'stream'
      });

      return new Promise((resolve, reject) => {
        let result = '';
        
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n').filter(line => line.trim());
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                resolve(result);
                return;
              }
              
              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;
                if (content) {
                  result += content;
                  onChunk(content, parsed);
                }
              } catch (parseError) {
                // Ignore parsing errors for partial chunks
              }
            }
          }
        });

        response.data.on('error', reject);
      });
    } catch (error) {
      this.logger.error(`Streaming chat completion failed: ${error.message}`);
      throw error;
    }
  }

  async proxyRequest(method, path, body, headers) {
    try {
      await this.ensureLMStudioRunning();
      
      const config = {
        method: method.toLowerCase(),
        url: path,
        headers: { ...headers },
        timeout: this.timeout
      };

      if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        config.data = body;
      }

      const response = await this.apiClient.request(config);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers
      };
    } catch (error) {
      this.logger.error(`Proxy request failed: ${error.message}`);
      throw error;
    }
  }

  async initialize() {
    this.logger.info('Initializing LM Studio service...');
    
    try {
      // Check if LM Studio CLI is available
      const cliPath = await this.findLMStudioCli();
      if (!cliPath) {
        this.logger.warn('LM Studio CLI not found. Some features may not work.');
        return false;
      }

      // Ensure LM Studio is running
      await this.ensureLMStudioRunning();
      
      // Get initial model information
      const downloadedModels = await this.getDownloadedModels();
      const loadedModels = await this.getLoadedModels();
      
      this.logger.info(`LM Studio service initialized successfully`);
      this.logger.info(`Downloaded models: ${downloadedModels.length}`);
      this.logger.info(`Loaded models: ${loadedModels.length}`);
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to initialize LM Studio service: ${error.message}`);
      return false;
    }
  }

  async shutdown() {
    this.logger.info('Shutting down LM Studio service...');
    
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
    
    // Optionally stop the LM Studio server
    // await this.stopLMStudio();
    
    this.logger.info('LM Studio service shut down');
  }
} 