import net from 'net';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class PortManager {
  constructor(logger) {
    this.logger = logger;
  }

  /**
   * Find a free port starting from the preferred port
   */
  async findFreePort(preferredPort = 8000, maxAttempts = 10) {
    for (let i = 0; i < maxAttempts; i++) {
      const port = preferredPort + i;
      if (await this.isPortFree(port)) {
        if (i > 0) {
          this.logger.info(`Port ${preferredPort} was busy, using port ${port} instead`);
        }
        return port;
      }
    }
    throw new Error(`Could not find a free port after ${maxAttempts} attempts starting from ${preferredPort}`);
  }

  /**
   * Check if a port is free
   */
  async isPortFree(port) {
    return new Promise((resolve) => {
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => {
          resolve(true);
        });
        server.close();
      });
      
      server.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Get process information for a specific port
   */
  async getPortInfo(port) {
    try {
      const isWindows = process.platform === 'win32';
      
      if (isWindows) {
        const { stdout } = await execAsync(`netstat -ano | findstr :${port}`);
        const lines = stdout.trim().split('\n').filter(line => line.includes(`${port}`));
        
        if (lines.length === 0) return null;
        
        const processes = [];
        for (const line of lines) {
          const parts = line.trim().split(/\s+/);
          const pid = parts[parts.length - 1];
          
          if (pid && pid !== '0') {
            try {
              const { stdout: processInfo } = await execAsync(`tasklist /FI "PID eq ${pid}" /FO CSV`);
              const processLines = processInfo.trim().split('\n');
              if (processLines.length > 1) {
                const processData = processLines[1].split(',');
                const processName = processData[0].replace(/"/g, '');
                processes.push({
                  pid: parseInt(pid),
                  name: processName,
                  port: port
                });
              }
            } catch (error) {
              // Process might have ended
            }
          }
        }
        return processes;
      } else {
        // Linux/Mac
        const { stdout } = await execAsync(`lsof -i :${port} -t`);
        const pids = stdout.trim().split('\n').filter(pid => pid);
        
        const processes = [];
        for (const pid of pids) {
          try {
            const { stdout: processInfo } = await execAsync(`ps -p ${pid} -o comm=`);
            processes.push({
              pid: parseInt(pid),
              name: processInfo.trim(),
              port: port
            });
          } catch (error) {
            // Process might have ended
          }
        }
        return processes;
      }
    } catch (error) {
      return null;
    }
  }

  /**
   * Kill processes running on a specific port
   */
  async killPortProcesses(port, force = false) {
    const processes = await this.getPortInfo(port);
    
    if (!processes || processes.length === 0) {
      this.logger.info(`No processes found running on port ${port}`);
      return [];
    }

    const killedProcesses = [];
    const isWindows = process.platform === 'win32';

    for (const process of processes) {
      try {
        this.logger.info(`Killing process ${process.name} (PID: ${process.pid}) on port ${port}`);
        
        if (isWindows) {
          await execAsync(`taskkill ${force ? '/F' : ''} /PID ${process.pid}`);
        } else {
          await execAsync(`kill ${force ? '-9' : '-15'} ${process.pid}`);
        }
        
        killedProcesses.push(process);
      } catch (error) {
        this.logger.warn(`Failed to kill process ${process.pid}: ${error.message}`);
      }
    }

    // Wait a bit for processes to actually terminate
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return killedProcesses;
  }

  /**
   * Kill Node.js processes (useful for development)
   */
  async killNodeProcesses(excludeCurrentProcess = true) {
    try {
      const isWindows = process.platform === 'win32';
      const currentPid = process.pid;
      
      if (isWindows) {
        const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq node.exe" /FO CSV');
        const lines = stdout.trim().split('\n').slice(1); // Skip header
        
        const killedProcesses = [];
        for (const line of lines) {
          const parts = line.split(',');
          if (parts.length >= 2) {
            const pid = parseInt(parts[1].replace(/"/g, ''));
            
            if (excludeCurrentProcess && pid === currentPid) {
              continue;
            }
            
            try {
              await execAsync(`taskkill /F /PID ${pid}`);
              killedProcesses.push({ pid, name: 'node.exe' });
              this.logger.info(`Killed Node.js process (PID: ${pid})`);
            } catch (error) {
              // Process might have already ended
            }
          }
        }
        return killedProcesses;
      } else {
        const { stdout } = await execAsync('pgrep node');
        const pids = stdout.trim().split('\n').filter(pid => pid);
        
        const killedProcesses = [];
        for (const pid of pids) {
          const pidNum = parseInt(pid);
          
          if (excludeCurrentProcess && pidNum === currentPid) {
            continue;
          }
          
          try {
            await execAsync(`kill -9 ${pidNum}`);
            killedProcesses.push({ pid: pidNum, name: 'node' });
            this.logger.info(`Killed Node.js process (PID: ${pidNum})`);
          } catch (error) {
            // Process might have already ended
          }
        }
        return killedProcesses;
      }
    } catch (error) {
      this.logger.warn(`Failed to kill Node.js processes: ${error.message}`);
      return [];
    }
  }

  /**
   * Clean up development ports (8000-8010, 3000-3010, 5173-5180)
   */
  async cleanupDevPorts() {
    const devPorts = [
      ...Array.from({ length: 11 }, (_, i) => 8000 + i), // 8000-8010
      ...Array.from({ length: 11 }, (_, i) => 3000 + i), // 3000-3010
      ...Array.from({ length: 8 }, (_, i) => 5173 + i),  // 5173-5180
    ];

    const cleanedPorts = [];
    
    for (const port of devPorts) {
      const processes = await this.getPortInfo(port);
      if (processes && processes.length > 0) {
        // Only kill Node.js processes on dev ports
        const nodeProcesses = processes.filter(p => 
          p.name.toLowerCase().includes('node') || 
          p.name.toLowerCase().includes('npm')
        );
        
        if (nodeProcesses.length > 0) {
          const killed = await this.killPortProcesses(port, true);
          if (killed.length > 0) {
            cleanedPorts.push({ port, processes: killed });
          }
        }
      }
    }

    if (cleanedPorts.length > 0) {
      this.logger.info(`Cleaned up ${cleanedPorts.length} development ports`);
    }

    return cleanedPorts;
  }

  /**
   * Get a summary of all used ports in development range
   */
  async getPortSummary() {
    const devPorts = [
      ...Array.from({ length: 11 }, (_, i) => 8000 + i),
      ...Array.from({ length: 11 }, (_, i) => 3000 + i),
      ...Array.from({ length: 8 }, (_, i) => 5173 + i),
    ];

    const usedPorts = [];
    
    for (const port of devPorts) {
      const processes = await this.getPortInfo(port);
      if (processes && processes.length > 0) {
        usedPorts.push({ port, processes });
      }
    }

    return usedPorts;
  }
} 