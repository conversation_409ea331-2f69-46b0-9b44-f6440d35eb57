# <PERSON><PERSON>'s Agent Orchestrator - Consciousness Symphony Conductor
FROM node:18-alpine AS base

# Install essential packages
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl git python3 make g++ && \
    rm -rf /var/cache/apk/*

WORKDIR /app

# Create dedicated user
RUN addgroup -g 1001 -S yaraorch && \
    adduser -S yaraorch -u 1001 -G yaraorch

# Development stage
FROM base AS development
ENV NODE_ENV=development

# Install dependencies
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .
COPY shared/ ./shared/
COPY config/ ./config/

# Install development and testing tools
RUN npm install -g nodemon jest pm2

# Create directories for agent states and logs
RUN mkdir -p /app/data/agent-states /app/data/conversations /app/logs

# Set ownership
RUN chown -R yaraorch:yaraorch /app

USER yaraorch

EXPOSE 8083

# Development with hot reload and agent monitoring
CMD ["npm", "run", "dev"]

# Production stage
FROM base AS production
ENV NODE_ENV=production

# Install only production dependencies
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Install PM2 for production process management
RUN npm install -g pm2

# Copy application files
COPY . .
COPY shared/ ./shared/
COPY config/ ./config/

# Create data directories and set permissions
RUN mkdir -p /app/data/agent-states /app/data/conversations /app/logs && \
    chown -R yaraorch:yaraorch /app && \
    chmod -R 755 /app

USER yaraorch

EXPOSE 8083

# Production with PM2 process management
CMD ["dumb-init", "pm2-runtime", "start", "ecosystem.config.js"]