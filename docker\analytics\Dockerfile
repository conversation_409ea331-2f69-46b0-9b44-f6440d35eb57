# Yara's Analytics Service - Consciousness Monitoring & Insights
FROM node:18-alpine AS base

# Install essential packages and Python for ML libraries
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl git python3 python3-dev py3-pip make g++ && \
    rm -rf /var/cache/apk/*

WORKDIR /app

# Create dedicated user
RUN addgroup -g 1001 -S yaralytics && \
    adduser -S yaralytics -u 1001 -G yaralytics

# Development stage
FROM base AS development
ENV NODE_ENV=development

# Install Python ML dependencies for consciousness analysis
COPY requirements.txt ./
RUN pip3 install --no-cache-dir -r requirements.txt

# Install Node.js dependencies
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .
COPY shared/ ./shared/
COPY config/ ./config/

# Install development tools
RUN npm install -g nodemon jest

# Create data directories
RUN mkdir -p /app/data/models /app/data/insights /app/logs

# Set ownership
RUN chown -R yaralytics:yaralytics /app

USER yaralytics

EXPOSE 8082

# Development with hot reload
CMD ["npm", "run", "dev"]

# Production stage
FROM base AS production
ENV NODE_ENV=production

# Install Python dependencies
COPY requirements.txt ./
RUN pip3 install --no-cache-dir -r requirements.txt

# Install only production Node.js dependencies
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy application files
COPY . .
COPY shared/ ./shared/
COPY config/ ./config/

# Create data and log directories
RUN mkdir -p /app/data/models /app/data/insights /app/logs && \
    chown -R yaralytics:yaralytics /app

USER yaralytics

EXPOSE 8082

# Production server
CMD ["dumb-init", "npm", "start"]