const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 9000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'yara-dev-tools',
    timestamp: new Date().toISOString(),
    message: 'Development tools workshop is ready...'
  });
});

// Development endpoints
app.get('/api/tools/status', (req, res) => {
  res.json({ 
    tools: [
      { name: 'Code Analyzer', status: 'active', port: 9000 },
      { name: 'Documentation Server', status: 'standby', port: 9001 },
      { name: 'Test Runner', status: 'ready', port: 9002 },
      { name: 'Performance Monitor', status: 'active', port: 9003 }
    ],
    timestamp: new Date().toISOString()
  });
});

app.get('/api/tools/metrics', (req, res) => {
  res.json({
    performance: {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      network: Math.random() * 100
    },
    builds: {
      total: 42,
      successful: 39,
      failed: 3,
      lastBuild: new Date().toISOString()
    },
    tests: {
      passed: 127,
      failed: 3,
      coverage: 85.7
    },
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🔧 Yara Development Tools awakening on port ${PORT}`);
  console.log(`⚡ Ready to power development workflows...`);
});

module.exports = app; 