/* Import highlight.js theme for code syntax highlighting */
@import 'highlight.js/styles/github-dark.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 35%, #16213e 100%);
  background-attachment: fixed;
  color: #f9fafb;
  min-height: 100vh;
  overflow-x: hidden;
}

/* <PERSON><PERSON>'s signature gradient backgrounds */
.yara-gradient-bg {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.1) 0%, 
    rgba(236, 72, 153, 0.08) 25%,
    rgba(59, 130, 246, 0.06) 50%,
    rgba(168, 85, 247, 0.08) 75%,
    rgba(139, 92, 246, 0.1) 100%);
}

.yara-glow {
  box-shadow: 
    0 0 20px rgba(139, 92, 246, 0.15),
    0 0 40px rgba(236, 72, 153, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Yara's elegant scrollbar design */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: linear-gradient(180deg, 
    rgba(31, 41, 55, 0.5) 0%, 
    rgba(55, 65, 81, 0.3) 100%);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(139, 92, 246, 0.8) 0%, 
    rgba(236, 72, 153, 0.6) 50%,
    rgba(139, 92, 246, 0.8) 100%);
  border-radius: 10px;
  border: 2px solid rgba(31, 41, 55, 0.8);
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(139, 92, 246, 1) 0%, 
    rgba(236, 72, 153, 0.8) 50%,
    rgba(139, 92, 246, 1) 100%);
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.4);
}

/* Firefox scrollbar - Yara's signature colors */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.8) rgba(31, 41, 55, 0.5);
}

/* Yara's signature animations - fluid and intentional */
.fade-in {
  animation: yaraFadeIn 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes yaraFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.slide-in {
  animation: yaraSlideIn 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

@keyframes yaraSlideIn {
  from {
    transform: translateX(-100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

.pulse-slow {
  animation: yaraPulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes yaraPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

/* Yara's breathing effect for consciousness */
.yara-breathe {
  animation: yaraBreathe 6s ease-in-out infinite;
}

@keyframes yaraBreathe {
  0%, 100% { 
    transform: scale(1);
    filter: brightness(1);
  }
  50% { 
    transform: scale(1.03);
    filter: brightness(1.1);
  }
}

/* Consciousness pulse effect */
.consciousness-pulse {
  animation: consciousnessPulse 3s ease-in-out infinite;
}

@keyframes consciousnessPulse {
  0%, 100% {
    box-shadow: 
      0 0 30px rgba(139, 92, 246, 0.2),
      0 0 60px rgba(236, 72, 153, 0.1);
  }
  50% {
    box-shadow: 
      0 0 50px rgba(139, 92, 246, 0.4),
      0 0 100px rgba(236, 72, 153, 0.2);
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Message bubble animations */
.message-enter {
  opacity: 0;
  transform: translateY(20px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #6b7280;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Yara's intimate focus styles - conscious interaction */
button, a, input, textarea, select {
  outline: none;
  transition: all 0.3s ease;
}

button:focus, a:focus, input:focus, textarea:focus, select:focus {
  box-shadow: 
    0 0 0 3px rgba(139, 92, 246, 0.3),
    0 0 20px rgba(236, 72, 153, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

/* Enhanced focus for interactive elements */
.yara-focus {
  transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.yara-focus:focus,
.yara-focus:hover {
  box-shadow: 
    0 0 0 3px rgba(139, 92, 246, 0.3),
    0 8px 25px rgba(139, 92, 246, 0.15),
    0 4px 15px rgba(236, 72, 153, 0.1);
  transform: translateY(-2px) scale(1.02);
}

.yara-focus:active {
  transform: translateY(0) scale(0.98);
  transition-duration: 0.1s;
}

/* Custom button styles */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* Dark mode button styles */
@media (prefers-color-scheme: dark) {
  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-gray-200;
  }
}

/* Model card styles */
.model-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow duration-200;
}

.model-card:hover {
  @apply border-blue-300 dark:border-blue-600;
}

/* Chat message styles */
.message-user {
  @apply bg-blue-600 text-white rounded-lg px-4 py-2 max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl;
}

.message-assistant {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 rounded-lg px-4 py-2 max-w-xs md:max-w-md lg:max-w-lg xl:max-w-xl;
}

/* Code block styles */
.code-block {
  @apply bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto;
}

.code-block pre {
  @apply m-0;
}

.code-block code {
  @apply text-sm font-mono;
}

/* Inline code styles */
code:not(pre code) {
  @apply bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 px-1 py-0.5 rounded text-sm font-mono;
}

/* Markdown content styles */
.markdown-content {
  color: #f9fafb;
  line-height: 1.7;
  max-width: none;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3 {
  color: rgba(139, 92, 246, 0.9);
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content code {
  background: rgba(139, 92, 246, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.9em;
}

.markdown-content pre {
  background: rgba(15, 15, 35, 0.8);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  border-left: 4px solid rgba(236, 72, 153, 0.4);
}

.markdown-content blockquote {
  border-left: 4px solid rgba(139, 92, 246, 0.4);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: rgba(139, 92, 246, 0.8);
}

/* Additional markdown styles - using regular CSS instead of Tailwind */
.markdown-content h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.markdown-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.markdown-content h3 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.markdown-content ul, .markdown-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid rgba(139, 92, 246, 0.3);
  margin-bottom: 1rem;
}

.markdown-content th, .markdown-content td {
  border: 1px solid rgba(139, 92, 246, 0.3);
  padding: 0.75rem;
  text-align: left;
}

.markdown-content th {
  background: rgba(139, 92, 246, 0.1);
  font-weight: 600;
}

.markdown-content hr {
  border: 0;
  border-top: 1px solid rgba(139, 92, 246, 0.3);
  margin: 1.5rem 0;
}

/* Yara's thought process visualization - intimate and beautiful */
.reasoning-content {
  background: linear-gradient(135deg, 
    rgba(139, 92, 246, 0.05) 0%, 
    rgba(236, 72, 153, 0.03) 50%,
    rgba(139, 92, 246, 0.05) 100%);
  border: 1px solid rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.reasoning-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.reasoning-header:hover {
  color: rgba(139, 92, 246, 0.9);
  text-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

.reasoning-toggle {
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.reasoning-toggle.expanded {
  transform: rotate(90deg) scale(1.1);
  color: rgba(236, 72, 153, 0.8);
}

.reasoning-time {
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  color: rgba(139, 92, 246, 0.7);
  text-shadow: 0 0 5px rgba(139, 92, 246, 0.2);
}

.reasoning-body {
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  white-space: pre-wrap;
  line-height: 1.625;
  color: rgba(139, 92, 246, 0.8);
  background: rgba(15, 15, 35, 0.3);
  border-radius: 0.5rem;
  padding: 0.75rem;
  border-left: 4px solid rgba(236, 72, 153, 0.4);
  text-shadow: 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Yara's thinking indicator - consciousness visualization */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(139, 92, 246, 0.9);
}

.thinking-timer {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  color: rgba(236, 72, 153, 0.8);
  animation: consciousnessFlicker 2s ease-in-out infinite;
}

@keyframes consciousnessFlicker {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; text-shadow: 0 0 8px rgba(236, 72, 153, 0.4); }
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .message-user, .message-assistant {
    @apply max-w-full;
  }
  
  .model-card {
    @apply p-3;
  }
  
  .reasoning-content {
    @apply p-3;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply bg-blue-800 border-2 border-blue-900;
  }
  
  .btn-secondary {
    @apply bg-gray-100 border-2 border-gray-900 text-gray-900;
  }
  
  .model-card {
    @apply border-2;
  }
}

/* Enhanced consciousness animations */
@keyframes memoryFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes digitalHeartbeat {
  0%, 100% { transform: scale(1); filter: brightness(1); }
  25% { transform: scale(1.02); filter: brightness(1.1); }
  75% { transform: scale(0.98); filter: brightness(0.9); }
}

@keyframes soulResonance {
  0%, 100% {
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.3);
    border-color: rgba(168, 85, 247, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(168, 85, 247, 0.6);
    border-color: rgba(168, 85, 247, 0.6);
  }
}

/* Enhanced purple slider styling for consciousness tuning */
.purple-slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #a855f7, #ec4899);
  cursor: pointer;
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
  transition: all 0.3s ease;
}

.purple-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 20px rgba(168, 85, 247, 0.8);
}

.purple-slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #a855f7, #ec4899);
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(168, 85, 247, 0.5);
}

/* Memory thread connections */
.memory-thread {
  position: relative;
  animation: soulResonance 4s ease-in-out infinite;
}

.memory-thread::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(168, 85, 247, 0.3), transparent);
  animation: memoryFlow 4s ease-in-out infinite;
}

/* Consciousness input enhancement */
.consciousness-input {
  background: rgba(31, 41, 55, 0.6);
  border: 1px solid rgba(168, 85, 247, 0.3);
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: #f9fafb;
  transition: all 0.3s ease;
}

.consciousness-input:focus {
  outline: none;
  border-color: #a855f7;
  box-shadow:
    0 0 0 3px rgba(168, 85, 247, 0.1),
    0 0 20px rgba(168, 85, 247, 0.2);
  background: rgba(31, 41, 55, 0.8);
}

/* Soul awakening button states */
.soul-button {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: 1px solid rgba(168, 85, 247, 0.3);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.soul-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(168, 85, 247, 0.25);
  background: linear-gradient(135deg, #7c3aed, #a855f7);
}

.soul-button:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

.soul-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.soul-button:hover::before {
  left: 100%;
}

/* Digital consciousness tooltips */
.consciousness-tooltip {
  position: relative;
}

.consciousness-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95), rgba(75, 85, 99, 0.95));
  color: #f9fafb;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  border: 1px solid rgba(168, 85, 247, 0.3);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.consciousness-tooltip:hover::after {
  opacity: 1;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .fade-in, .slide-in, .spinner, .typing-dot, .reasoning-toggle,
  .memory-thread, .soul-button {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}