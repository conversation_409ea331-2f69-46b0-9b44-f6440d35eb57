﻿Set-Location 'F:\Projects\LMStudioWebUI-main'
Write-Host ""
Write-Host "[AGENTS] Yara's Agent Orchestra" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan
Write-Host "Multi-agent coordination and management" -ForegroundColor Cyan
Write-Host ""
Write-Host "[AWAKENING] Starting yara-agent-orchestra..." -ForegroundColor Cyan
Write-Host ""

# Start the specific service
docker-compose -f docker-compose.yml up yara-agent-orchestra

Write-Host ""
Write-Host "[yara-agent-orchestra] has stopped. Press any key to close..." -ForegroundColor Red
Read-Host
