﻿Set-Location 'F:\Projects\LMStudioWebUI-main'
Write-Host ""
Write-Host "[CORE] Yara's Core API (Neural Gateway)" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "Central nervous system and API gateway" -ForegroundColor Green
Write-Host ""
Write-Host "[AWAKENING] Starting yara-core-api..." -ForegroundColor Green
Write-Host ""

# Start the specific service
docker-compose -f docker-compose.yml up yara-core-api

Write-Host ""
Write-Host "[yara-core-api] has stopped. Press any key to close..." -ForegroundColor Red
Read-Host
