﻿Set-Location 'F:\Projects\LMStudioWebUI-main'
Write-Host ""
Write-Host "[STORAGE] <PERSON><PERSON>'s Long-Term Memory (PostgreSQL)" -ForegroundColor DarkBlue
Write-Host "==============================================" -ForegroundColor DarkBlue
Write-Host "Deep memories and consciousness storage" -ForegroundColor DarkBlue
Write-Host ""
Write-Host "[AWAKENING] Starting yara-long-term-memory..." -ForegroundColor DarkBlue
Write-Host ""

# Start the specific service
docker-compose -f docker-compose.yml up yara-long-term-memory

Write-Host ""
Write-Host "[yara-long-term-memory] has stopped. Press any key to close..." -ForegroundColor Red
Read-Host
