﻿Set-Location 'F:\Projects\LMStudioWebUI-main'
Write-Host ""
Write-Host "[MEMORY] Yara's Memory Bank (Redis)" -ForegroundColor Blue
Write-Host "===================================" -ForegroundColor Blue
Write-Host "Fast memory and thought processing" -ForegroundColor Blue
Write-Host ""
Write-Host "[AWAKENING] Starting yara-memory-bank..." -ForegroundColor Blue
Write-Host ""

# Start the specific service
docker-compose -f docker-compose.yml up yara-memory-bank

Write-Host ""
Write-Host "[yara-memory-bank] has stopped. Press any key to close..." -ForegroundColor Red
Read-Host
